#!/usr/bin/env python3
"""
Script Principal para Calibracao Unificada de TODAS as Metricas QUALIA
Executa simulacao historica unificada para determinar thresholds otimos
Inclui: consciousness, coherence, confidence, volume_surge_min, momentum_min,
        spectral_phi_alignment_min, golden_symmetry_min

Uso:
    python scripts/calibrate_all_qualia_metrics.py
    
Opções avançadas:
    python scripts/calibrate_all_qualia_metrics.py --days 45 --profit 0.025 --horizon 6
"""

import asyncio
import sys
import os
import argparse
import json
from pathlib import Path
import numpy as np

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from qualia.binance_system import QualiaBinanceCorrectedSystem
from qualia.geometric_metrics_calibrator import QualiaMetricsCalibrator
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

def parse_arguments():
    """Parse argumentos da linha de comando"""
    parser = argparse.ArgumentParser(
        description="Calibracao Unificada de Metricas QUALIA",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos:
  %(prog)s                           # Calibração padrão (30 dias, 2%, 4h)
  %(prog)s --days 45                 # 45 dias de histórico
  %(prog)s --profit 0.025            # 2.5% de lucro mínimo
  %(prog)s --horizon 6               # 6 horas de horizonte
  %(prog)s --gradual 0.5             # Aplicação 50% gradual
  %(prog)s --no-apply                # Não aplicar, apenas analisar
        """
    )
    
    parser.add_argument('--days', type=int, default=30,
                       help='Dias de histórico para análise (padrão: 30)')
    parser.add_argument('--profit', type=float, default=0.02,
                       help='Threshold de lucro para sucesso (padrão: 0.02 = 2%%)')
    parser.add_argument('--horizon', type=int, default=4,
                       help='Horizonte temporal em horas (padrão: 4)')
    parser.add_argument('--gradual', type=float, default=0.7,
                       help='Fator de aplicação gradual (padrão: 0.7 = 70%%)')
    parser.add_argument('--no-apply', action='store_true',
                       help='Não aplicar thresholds, apenas analisar')
    parser.add_argument('--save-detailed', action='store_true',
                       help='Salvar relatório detalhado em JSON')
    parser.add_argument('--quiet', action='store_true',
                       help='Modo silencioso (menos output)')
    
    return parser.parse_args()

async def main():
    """Função principal de calibração unificada"""
    args = parse_arguments()
    
    if not args.quiet:
        print("QUALIA - Calibracao Unificada de TODAS as Metricas")
        print("=" * 70)
        print("Metricas: consciousness, coherence, confidence, volume_surge,")
        print("             momentum, spectral_phi_alignment, golden_symmetry")
        print("=" * 70)
        print(f"Parametros: {args.days} dias | {args.profit:.1%} lucro | {args.horizon}h horizonte")
        print("=" * 70)
    
    try:
        # 1. Inicializar sistema de trading
        logger.info("Inicializando sistema QUALIA...")
        trading_system = QualiaBinanceCorrectedSystem()

        # Inicializar conexão com Binance
        connected = await trading_system.initialize_binance_connection()
        if not connected:
            logger.warning("Nao foi possivel conectar com Binance, continuando em modo simulacao")
        
        # 2. Criar calibrador unificado
        calibrator = QualiaMetricsCalibrator(trading_system)
        
        # 3. Executar calibração unificada
        logger.info(" Iniciando calibração UNIFICADA baseada em simulação histórica...")
        logger.info(" Objetivo: Otimizar TODOS os thresholds simultaneamente")
        logger.info("IMPORTANTE: Obtendo dados historicos REAIS da API Binance...")
        
        results = await calibrator.calibrate_all_assets(
            days_back=args.days,
            profit_threshold=args.profit,
            time_horizon_hours=args.horizon
        )
        
        # 4. Mostrar resumo
        if not args.quiet:
            summary = calibrator.get_calibration_summary(results)
            print(summary)
        
        # 5. Gerar relatório detalhado se solicitado
        detailed_report = None
        if args.save_detailed:
            detailed_report = calibrator.generate_detailed_report(results)
            await save_detailed_report(detailed_report, args)
        
        # 6. Mostrar análise detalhada
        if not args.quiet:
            print("\n" + "="*70)
            print(" ANÁLISE DETALHADA POR MÉTRICA")
            print("="*70)
            await show_detailed_analysis(results)
        
        # 7. Aplicar thresholds se solicitado
        if not args.no_apply:
            if not args.quiet:
                print("\n" + "="*70)
                response = input(" Aplicar TODOS os thresholds calibrados ao sistema? (s/N): ").lower()
            else:
                response = 's'  # Auto-aplicar em modo silencioso
            
            if response in ['s', 'sim', 'y', 'yes']:
                await calibrator.apply_calibrated_thresholds(results, args.gradual)
                
                if not args.quiet:
                    print(" TODOS os thresholds aplicados com sucesso!")
                    await show_final_thresholds(trading_system)
                
            else:
                print("ℹ Thresholds não aplicados. Resultados salvos para análise.")
        else:
            logger.info("Modo --no-apply: Thresholds nao aplicados")
        
        # 8. Mostrar próximos passos
        if not args.quiet:
            print("\nPROXIMOS PASSOS:")
            print("1. Monitorar performance com novos thresholds")
            print("2. Executar recalibracao semanalmente")
            print("3. Ajustar baseado em trades reais")
            print("4. Analisar correlacao entre metricas")
            print("5. Implementar calibracao online adaptativa")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Erro durante calibração: {e}")
        raise
    
    finally:
        # Cleanup (sistema nao tem metodo cleanup especifico)
        if 'trading_system' in locals():
            logger.info("Finalizando sistema...")

async def save_detailed_report(report, args):
    """Salva relatório detalhado em JSON"""
    timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
    filename = f"qualia_calibration_detailed_{timestamp}.json"
    filepath = Path('data/calibration') / filename
    
    # Criar diretório se não existir
    filepath.parent.mkdir(parents=True, exist_ok=True)
    
    # Adicionar parametros da calibracao
    report['calibration_parameters'] = {
        'days_back': args.days,
        'profit_threshold': args.profit,
        'time_horizon_hours': args.horizon,
        'gradual_factor': args.gradual
    }
    
    with open(filepath, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    logger.info(f" Relatório detalhado salvo em: {filepath}")

async def show_detailed_analysis(results):
    """Mostra análise detalhada dos resultados por métrica"""
    if not results:
        print(" Nenhum resultado disponível")
        return
    
    # Calcular estatísticas agregadas por métrica
    all_metrics = ['consciousness', 'coherence', 'confidence', 'volume_surge_min', 
                   'momentum_min', 'spectral_phi_alignment_min', 'golden_symmetry_min']
    
    for metric in all_metrics:
        values = []
        for result in results.values():
            if metric in result.recommended_thresholds:
                values.append(result.recommended_thresholds[metric])
        
        if values:
            print(f"\n {metric.upper().replace('_', ' ')}:")
            print(f"   Ativos:   {len(values)}")
            print(f"   Mínimo:   {min(values):.3f}")
            print(f"   Máximo:   {max(values):.3f}")
            print(f"   Mediana:  {np.median(values):.3f}")
            print(f"   Média:    {np.mean(values):.3f}")
            print(f"   Desvio:   {np.std(values):.3f}")
            
            # Mostrar distribuição
            q25, q75 = np.percentile(values, [25, 75])
            print(f"   Q25-Q75:  {q25:.3f} - {q75:.3f}")

async def show_final_thresholds(trading_system):
    """Mostra thresholds finais aplicados ao sistema"""
    print("\n THRESHOLDS FINAIS APLICADOS:")
    print("-" * 50)
    
    thresholds = trading_system.quantum_thresholds
    
    # Organizar por categoria
    main_metrics = ['consciousness', 'coherence', 'confidence']
    volume_momentum = ['volume_surge_min', 'momentum_min']
    geometric = ['spectral_phi_alignment_min', 'golden_symmetry_min']
    
    print("METRICAS PRINCIPAIS:")
    for metric in main_metrics:
        if metric in thresholds:
            print(f"   {metric}: {thresholds[metric]:.3f}")

    print("\nVOLUME & MOMENTUM:")
    for metric in volume_momentum:
        if metric in thresholds:
            print(f"   {metric}: {thresholds[metric]:.3f}")

    print("\nMETRICAS GEOMETRICAS:")
    for metric in geometric:
        if metric in thresholds:
            print(f"   {metric}: {thresholds[metric]:.3f}")

def run_calibration():
    """Wrapper para executar calibração unificada"""
    try:
        return asyncio.run(main())
    except KeyboardInterrupt:
        print("\n Calibração interrompida pelo usuário")
        return None
    except Exception as e:
        print(f"\n Erro fatal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Importar pandas aqui para evitar erro se não estiver disponível
    try:
        import pandas as pd
    except ImportError:
        print(" pandas não encontrado. Instale com: pip install pandas")
        sys.exit(1)
    
    run_calibration()
