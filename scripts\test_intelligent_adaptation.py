#!/usr/bin/env python3
"""
Script para testar o Sistema de Adaptação Inteligente
Simula diferentes cenários de mercado e verifica comportamento do sistema
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from qualia.config_manager import get_config_manager
from qualia.intelligent_adaptation_system import IntelligentAdaptationSystem, AdaptationState

def test_intelligent_adaptation():
    """Testa o sistema de adaptação inteligente"""
    
    print("TESTE DO SISTEMA DE ADAPTAÇÃO INTELIGENTE")
    print("=" * 60)
    
    # Carregar configuração
    config_manager = get_config_manager('config/qualia_config.yaml')
    
    # Thresholds calibrados (valores atuais do sistema)
    calibrated_thresholds = {
        'consciousness': 0.854,
        'coherence': 0.820,
        'confidence': 0.719,
        'volume_surge_min': 0.856,
        'momentum_min': 0.008,
        'spectral_phi_alignment_min': 0.538,
        'golden_symmetry_min': 0.634
    }
    
    # Inicializar sistema
    adaptation_system = IntelligentAdaptationSystem(
        config_manager=config_manager,
        calibrated_thresholds=calibrated_thresholds
    )
    
    print(f"Sistema inicializado: {adaptation_system.current_state.value}")
    print(f"Configuração habilitada: {adaptation_system.config['enabled']}")
    print()
    
    # CENÁRIO 1: Taxa de aprovação muito baixa (sistema muito restritivo)
    print("CENÁRIO 1: Taxa de aprovação muito baixa (2%)")
    print("-" * 40)
    
    for cycle in range(1, 6):
        # Simular taxa muito baixa
        pass_rate = 0.02  # 2%
        assets_analyzed = 50
        signals_found = 1
        
        updated_thresholds = adaptation_system.process_cycle(pass_rate, assets_analyzed, signals_found)
        
        print(f"Ciclo {cycle}:")
        print(f"  Taxa: {pass_rate:.1%}, Estado: {adaptation_system.current_state.value}")
        print(f"  Consciousness: {updated_thresholds['consciousness']:.3f}")
        print(f"  Coherence: {updated_thresholds['coherence']:.3f}")
        print()
    
    # CENÁRIO 2: Taxa de aprovação muito alta (sistema muito permissivo)
    print("CENÁRIO 2: Taxa de aprovação muito alta (40%)")
    print("-" * 40)
    
    # Reset para valores calibrados
    adaptation_system.reset_to_calibrated("Teste cenário 2")
    
    for cycle in range(1, 6):
        # Simular taxa muito alta
        pass_rate = 0.40  # 40%
        assets_analyzed = 50
        signals_found = 20
        
        updated_thresholds = adaptation_system.process_cycle(pass_rate, assets_analyzed, signals_found)
        
        print(f"Ciclo {cycle}:")
        print(f"  Taxa: {pass_rate:.1%}, Estado: {adaptation_system.current_state.value}")
        print(f"  Consciousness: {updated_thresholds['consciousness']:.3f}")
        print(f"  Coherence: {updated_thresholds['coherence']:.3f}")
        print()
    
    # CENÁRIO 3: Sem sinais por vários ciclos
    print("CENÁRIO 3: Sem sinais por vários ciclos")
    print("-" * 40)
    
    # Reset para valores calibrados
    adaptation_system.reset_to_calibrated("Teste cenário 3")
    
    for cycle in range(1, 8):
        # Simular sem sinais
        pass_rate = 0.0  # 0%
        assets_analyzed = 50
        signals_found = 0
        
        updated_thresholds = adaptation_system.process_cycle(pass_rate, assets_analyzed, signals_found)
        
        print(f"Ciclo {cycle}:")
        print(f"  Taxa: {pass_rate:.1%}, Estado: {adaptation_system.current_state.value}")
        print(f"  Ciclos sem sinais: {adaptation_system.metrics.cycles_without_signals}")
        print(f"  Consciousness: {updated_thresholds['consciousness']:.3f}")
        print()
    
    # CENÁRIO 4: Retorno gradual aos valores calibrados
    print("CENÁRIO 4: Retorno gradual aos valores calibrados")
    print("-" * 40)
    
    # Simular passagem de tempo para trigger de retorno
    adaptation_system.metrics.cycles_since_last_adaptation = 60  # Mais que o trigger
    
    for cycle in range(1, 10):
        # Taxa normal
        pass_rate = 0.15  # 15% - dentro da faixa ideal
        assets_analyzed = 50
        signals_found = 7
        
        updated_thresholds = adaptation_system.process_cycle(pass_rate, assets_analyzed, signals_found)
        
        print(f"Ciclo {cycle}:")
        print(f"  Taxa: {pass_rate:.1%}, Estado: {adaptation_system.current_state.value}")
        print(f"  Consciousness: {updated_thresholds['consciousness']:.3f}")
        
        # Parar se retornou completamente
        if adaptation_system.current_state == AdaptationState.CALIBRATED:
            print("  RETORNO COMPLETO aos valores calibrados!")
            break
        print()
    
    # RELATÓRIO FINAL
    print("\n" + "=" * 60)
    print("RELATÓRIO FINAL")
    print("=" * 60)
    
    status_report = adaptation_system.get_status_report()
    
    print(f"Estado final: {status_report['system_status']['current_state']}")
    print(f"Total de adaptações: {status_report['adaptation_history']['total_adaptations']}")
    print()
    
    print("THRESHOLDS FINAIS vs CALIBRADOS:")
    for metric, values in status_report['thresholds']['deviations'].items():
        calibrated = status_report['thresholds']['calibrated'][metric]
        current = status_report['thresholds']['current'][metric]
        deviation = values['percentage']
        
        print(f"  {metric}:")
        print(f"    Calibrado: {calibrated:.3f}")
        print(f"    Atual: {current:.3f}")
        print(f"    Desvio: {deviation:+.1f}%")
    
    print("\nÚLTIMAS ADAPTAÇÕES:")
    for i, adaptation in enumerate(status_report['adaptation_history']['recent_adaptations'][-3:], 1):
        print(f"  {i}. {adaptation['reason']} -> {adaptation['state_after']}")
        print(f"     {adaptation['timestamp']}")
    
    # Salvar relatório
    report_file = Path('data/adaptation/test_report.json')
    report_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(status_report, f, indent=2, ensure_ascii=False)
    
    print(f"\nRelatório completo salvo em: {report_file}")
    
    return adaptation_system

def test_configuration_scenarios():
    """Testa diferentes configurações do sistema"""
    
    print("\n" + "=" * 60)
    print("TESTE DE CONFIGURAÇÕES")
    print("=" * 60)
    
    # Testar com sistema desabilitado
    print("1. Sistema desabilitado:")
    
    config_manager = get_config_manager('config/qualia_config.yaml')
    
    # Simular sistema desabilitado
    original_enabled = config_manager.config['quantum_thresholds']['intelligent_adaptation']['enabled']
    config_manager.config['quantum_thresholds']['intelligent_adaptation']['enabled'] = False
    
    calibrated_thresholds = {
        'consciousness': 0.854,
        'coherence': 0.820,
        'confidence': 0.719,
        'volume_surge_min': 0.856,
        'momentum_min': 0.008,
        'spectral_phi_alignment_min': 0.538,
        'golden_symmetry_min': 0.634
    }
    
    disabled_system = IntelligentAdaptationSystem(
        config_manager=config_manager,
        calibrated_thresholds=calibrated_thresholds
    )
    
    # Tentar processar ciclo
    result = disabled_system.process_cycle(0.02, 50, 1)  # Taxa baixa
    
    print(f"   Habilitado: {disabled_system.config['enabled']}")
    print(f"   Estado: {disabled_system.current_state.value}")
    print(f"   Thresholds mantidos: {result == calibrated_thresholds}")
    
    # Restaurar configuração
    config_manager.config['quantum_thresholds']['intelligent_adaptation']['enabled'] = original_enabled
    
    print("\n2. Diferentes parâmetros de adaptação:")
    
    # Testar com ajustes mais agressivos
    config_manager.config['quantum_thresholds']['intelligent_adaptation']['adaptation_parameters']['restrictive_adjustment'] = -0.25
    config_manager.config['quantum_thresholds']['intelligent_adaptation']['adaptation_parameters']['permissive_adjustment'] = 0.25
    
    aggressive_system = IntelligentAdaptationSystem(
        config_manager=config_manager,
        calibrated_thresholds=calibrated_thresholds
    )
    
    # Processar ciclo com taxa baixa
    result = aggressive_system.process_cycle(0.02, 50, 1)
    
    print(f"   Ajuste restritivo: -25%")
    print(f"   Consciousness: {calibrated_thresholds['consciousness']:.3f} -> {result['consciousness']:.3f}")
    print(f"   Mudança: {((result['consciousness'] - calibrated_thresholds['consciousness']) / calibrated_thresholds['consciousness']) * 100:+.1f}%")

if __name__ == "__main__":
    try:
        # Executar testes
        adaptation_system = test_intelligent_adaptation()
        test_configuration_scenarios()
        
        print("\n" + "=" * 60)
        print("TODOS OS TESTES CONCLUÍDOS COM SUCESSO!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\nERRO durante teste: {e}")
        import traceback
        traceback.print_exc()
