#!/usr/bin/env python3
"""
Sistema de Adaptação Inteligente para QUALIA
Preserva thresholds calibrados como base, permitindo ajustes controlados quando necessário
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum

from qsi.qualia.utils.logger import QualiaLogger

logger = QualiaLogger("IntelligentAdaptation")

class AdaptationReason(Enum):
    """Razões para adaptação de thresholds"""
    LOW_PASS_RATE = "low_pass_rate"
    HIGH_PASS_RATE = "high_pass_rate"
    NO_SIGNALS = "no_signals"
    EMERGENCY = "emergency"
    GRADUAL_RETURN = "gradual_return"
    MANUAL = "manual"

class AdaptationState(Enum):
    """Estados do sistema de adaptação"""
    CALIBRATED = "calibrated"          # Usando valores calibrados originais
    ADAPTED_RESTRICTIVE = "restrictive" # Adaptado para ser menos restritivo
    ADAPTED_PERMISSIVE = "permissive"   # Adaptado para ser mais restritivo
    RETURNING = "returning"             # Retornando gradualmente aos valores calibrados
    EMERGENCY = "emergency"             # Modo emergência

@dataclass
class AdaptationRecord:
    """Registro de uma adaptação"""
    timestamp: str
    reason: AdaptationReason
    state_before: AdaptationState
    state_after: AdaptationState
    thresholds_before: Dict[str, float]
    thresholds_after: Dict[str, float]
    pass_rate_before: float
    cycles_without_signals: int
    adjustment_percentage: float
    notes: str = ""

@dataclass
class AdaptationMetrics:
    """Métricas para decisão de adaptação"""
    current_pass_rate: float
    cycles_without_signals: int
    total_assets_analyzed: int
    consecutive_low_rate_cycles: int
    consecutive_high_rate_cycles: int
    time_since_last_adaptation: float
    cycles_since_last_adaptation: int

class IntelligentAdaptationSystem:
    """
    Sistema de Adaptação Inteligente que preserva thresholds calibrados
    como base e permite ajustes controlados quando necessário
    """
    
    def __init__(self, config_manager, calibrated_thresholds: Dict[str, float]):
        self.config_manager = config_manager
        self.calibrated_thresholds = calibrated_thresholds.copy()
        self.current_thresholds = calibrated_thresholds.copy()
        
        # Estado do sistema
        self.current_state = AdaptationState.CALIBRATED
        self.adaptation_history: List[AdaptationRecord] = []
        
        # Métricas de monitoramento
        self.metrics = AdaptationMetrics(
            current_pass_rate=0.0,
            cycles_without_signals=0,
            total_assets_analyzed=0,
            consecutive_low_rate_cycles=0,
            consecutive_high_rate_cycles=0,
            time_since_last_adaptation=0.0,
            cycles_since_last_adaptation=0
        )
        
        # Configurações
        self.config = self._load_config()
        
        # Histórico de performance
        self.performance_history: List[Dict] = []
        self.last_adaptation_time = datetime.now()
        
        # Criar diretório para histórico
        if self.config['logging']['save_adaptation_history']:
            history_path = Path(self.config['logging']['history_file'])
            history_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.info("Sistema de Adaptação Inteligente inicializado")
        logger.info(f"Estado inicial: {self.current_state.value}")
        logger.info(f"Thresholds calibrados carregados: {len(self.calibrated_thresholds)} métricas")
    
    def _load_config(self) -> Dict:
        """Carrega configurações do sistema"""
        base_path = "quantum_thresholds.intelligent_adaptation"

        def get_config_value(path: str, default_value):
            """Helper para obter valor com fallback"""
            try:
                return self.config_manager.get(path)
            except:
                return default_value

        config = {
            'enabled': get_config_value(f'{base_path}.enabled', True),
            'use_calibrated_as_base': get_config_value(f'{base_path}.use_calibrated_as_base', True),

            'activation_criteria': {
                'min_pass_rate_threshold': get_config_value(f'{base_path}.activation_criteria.min_pass_rate_threshold', 0.05),
                'max_pass_rate_threshold': get_config_value(f'{base_path}.activation_criteria.max_pass_rate_threshold', 0.35),
                'max_cycles_without_signals': get_config_value(f'{base_path}.activation_criteria.max_cycles_without_signals', 5),
                'min_assets_for_analysis': get_config_value(f'{base_path}.activation_criteria.min_assets_for_analysis', 10),
            },

            'adaptation_parameters': {
                'restrictive_adjustment': get_config_value(f'{base_path}.adaptation_parameters.restrictive_adjustment', -0.15),
                'permissive_adjustment': get_config_value(f'{base_path}.adaptation_parameters.permissive_adjustment', 0.15),
                'emergency_adjustment': get_config_value(f'{base_path}.adaptation_parameters.emergency_adjustment', -0.25),
                'max_deviation_from_calibrated': get_config_value(f'{base_path}.adaptation_parameters.max_deviation_from_calibrated', 0.20),
                'min_threshold_floor': get_config_value(f'{base_path}.adaptation_parameters.min_threshold_floor', 0.30),
                'max_threshold_ceiling': get_config_value(f'{base_path}.adaptation_parameters.max_threshold_ceiling', 0.95),
            },

            'gradual_return': {
                'enabled': get_config_value(f'{base_path}.gradual_return.enabled', True),
                'return_trigger_hours': get_config_value(f'{base_path}.gradual_return.return_trigger_hours', 24),
                'return_trigger_cycles': get_config_value(f'{base_path}.gradual_return.return_trigger_cycles', 50),
                'return_step_percentage': get_config_value(f'{base_path}.gradual_return.return_step_percentage', 0.05),
                'return_evaluation_interval': get_config_value(f'{base_path}.gradual_return.return_evaluation_interval', 10),
            },

            'logging': {
                'log_all_adaptations': get_config_value(f'{base_path}.logging.log_all_adaptations', True),
                'log_performance_metrics': get_config_value(f'{base_path}.logging.log_performance_metrics', True),
                'save_adaptation_history': get_config_value(f'{base_path}.logging.save_adaptation_history', True),
                'history_file': get_config_value(f'{base_path}.logging.history_file', "data/adaptation/adaptation_history.json"),
            },

            'advanced': {
                'adaptation_cooldown_cycles': get_config_value(f'{base_path}.advanced.adaptation_cooldown_cycles', 20),
                'require_confirmation_cycles': get_config_value(f'{base_path}.advanced.require_confirmation_cycles', 3),
                'emergency_mode_threshold': get_config_value(f'{base_path}.advanced.emergency_mode_threshold', 0.01),
                'stability_check_window': get_config_value(f'{base_path}.advanced.stability_check_window', 20),
            }
        }

        return config
    
    def update_metrics(self, pass_rate: float, assets_analyzed: int, signals_found: int):
        """Atualiza métricas de monitoramento"""
        self.metrics.current_pass_rate = pass_rate
        self.metrics.total_assets_analyzed = assets_analyzed
        
        # Atualizar contadores
        if signals_found == 0:
            self.metrics.cycles_without_signals += 1
        else:
            self.metrics.cycles_without_signals = 0
        
        # Contadores de taxa consecutiva
        if pass_rate < self.config['activation_criteria']['min_pass_rate_threshold']:
            self.metrics.consecutive_low_rate_cycles += 1
            self.metrics.consecutive_high_rate_cycles = 0
        elif pass_rate > self.config['activation_criteria']['max_pass_rate_threshold']:
            self.metrics.consecutive_high_rate_cycles += 1
            self.metrics.consecutive_low_rate_cycles = 0
        else:
            self.metrics.consecutive_low_rate_cycles = 0
            self.metrics.consecutive_high_rate_cycles = 0
        
        # Tempo desde última adaptação
        self.metrics.time_since_last_adaptation = (datetime.now() - self.last_adaptation_time).total_seconds() / 3600
        self.metrics.cycles_since_last_adaptation += 1
        
        # Salvar no histórico de performance
        self.performance_history.append({
            'timestamp': datetime.now().isoformat(),
            'pass_rate': pass_rate,
            'assets_analyzed': assets_analyzed,
            'signals_found': signals_found,
            'state': self.current_state.value
        })
        
        # Manter apenas últimos 1000 registros
        if len(self.performance_history) > 1000:
            self.performance_history = self.performance_history[-1000:]
        
        if self.config['logging']['log_performance_metrics']:
            logger.debug(f"Métricas atualizadas: Taxa={pass_rate:.1%}, Ativos={assets_analyzed}, "
                        f"Sinais={signals_found}, Ciclos sem sinais={self.metrics.cycles_without_signals}")

    def should_adapt(self) -> Tuple[bool, AdaptationReason, float]:
        """
        Determina se deve adaptar thresholds

        Returns:
            Tuple[bool, AdaptationReason, float]: (deve_adaptar, razão, ajuste_percentual)
        """
        if not self.config['enabled']:
            return False, None, 0.0

        # Verificar se há dados suficientes
        if self.metrics.total_assets_analyzed < self.config['activation_criteria']['min_assets_for_analysis']:
            return False, None, 0.0

        # Verificar cooldown
        if self.metrics.cycles_since_last_adaptation < self.config['advanced']['adaptation_cooldown_cycles']:
            return False, None, 0.0

        # Verificar modo emergência (taxa extremamente baixa)
        if self.metrics.current_pass_rate < self.config['advanced']['emergency_mode_threshold']:
            if self.metrics.consecutive_low_rate_cycles >= self.config['advanced']['require_confirmation_cycles']:
                return True, AdaptationReason.EMERGENCY, self.config['adaptation_parameters']['emergency_adjustment']

        # Verificar ciclos sem sinais
        if self.metrics.cycles_without_signals >= self.config['activation_criteria']['max_cycles_without_signals']:
            return True, AdaptationReason.NO_SIGNALS, self.config['adaptation_parameters']['restrictive_adjustment']

        # Verificar taxa muito baixa
        if self.metrics.current_pass_rate < self.config['activation_criteria']['min_pass_rate_threshold']:
            if self.metrics.consecutive_low_rate_cycles >= self.config['advanced']['require_confirmation_cycles']:
                return True, AdaptationReason.LOW_PASS_RATE, self.config['adaptation_parameters']['restrictive_adjustment']

        # Verificar taxa muito alta
        if self.metrics.current_pass_rate > self.config['activation_criteria']['max_pass_rate_threshold']:
            if self.metrics.consecutive_high_rate_cycles >= self.config['advanced']['require_confirmation_cycles']:
                return True, AdaptationReason.HIGH_PASS_RATE, self.config['adaptation_parameters']['permissive_adjustment']

        return False, None, 0.0

    def should_return_to_calibrated(self) -> bool:
        """Determina se deve iniciar retorno gradual aos valores calibrados"""
        if not self.config['gradual_return']['enabled']:
            return False

        if self.current_state == AdaptationState.CALIBRATED:
            return False

        # Verificar triggers de tempo ou ciclos
        time_trigger = self.metrics.time_since_last_adaptation >= self.config['gradual_return']['return_trigger_hours']
        cycles_trigger = self.metrics.cycles_since_last_adaptation >= self.config['gradual_return']['return_trigger_cycles']

        # Verificar se é hora de avaliar retorno
        evaluation_interval = self.config['gradual_return']['return_evaluation_interval']
        is_evaluation_cycle = self.metrics.cycles_since_last_adaptation % evaluation_interval == 0

        return (time_trigger or cycles_trigger) and is_evaluation_cycle

    def apply_adaptation(self, reason: AdaptationReason, adjustment_percentage: float) -> Dict[str, float]:
        """
        Aplica adaptação aos thresholds

        Args:
            reason: Razão da adaptação
            adjustment_percentage: Percentual de ajuste (negativo = menos restritivo, positivo = mais restritivo)

        Returns:
            Dict com os novos thresholds
        """
        old_thresholds = self.current_thresholds.copy()
        old_state = self.current_state

        # Aplicar ajuste percentual aos valores calibrados
        new_thresholds = {}
        for metric, calibrated_value in self.calibrated_thresholds.items():
            # Calcular novo valor
            adjustment = calibrated_value * adjustment_percentage
            new_value = calibrated_value + adjustment

            # Aplicar limites de segurança
            max_deviation = self.config['adaptation_parameters']['max_deviation_from_calibrated']
            min_allowed = calibrated_value * (1 - max_deviation)
            max_allowed = calibrated_value * (1 + max_deviation)

            # Aplicar limites absolutos
            min_floor = self.config['adaptation_parameters']['min_threshold_floor']
            max_ceiling = self.config['adaptation_parameters']['max_threshold_ceiling']

            new_value = max(min_allowed, min(max_allowed, new_value))
            new_value = max(min_floor, min(max_ceiling, new_value))

            new_thresholds[metric] = round(new_value, 3)

        # Determinar novo estado
        if reason == AdaptationReason.EMERGENCY:
            new_state = AdaptationState.EMERGENCY
        elif adjustment_percentage < 0:
            new_state = AdaptationState.ADAPTED_RESTRICTIVE
        else:
            new_state = AdaptationState.ADAPTED_PERMISSIVE

        # Criar registro da adaptação
        record = AdaptationRecord(
            timestamp=datetime.now().isoformat(),
            reason=reason,
            state_before=old_state,
            state_after=new_state,
            thresholds_before=old_thresholds,
            thresholds_after=new_thresholds,
            pass_rate_before=self.metrics.current_pass_rate,
            cycles_without_signals=self.metrics.cycles_without_signals,
            adjustment_percentage=adjustment_percentage,
            notes=f"Adaptação automática: {reason.value}"
        )

        # Atualizar estado
        self.current_thresholds = new_thresholds
        self.current_state = new_state
        self.adaptation_history.append(record)
        self.last_adaptation_time = datetime.now()
        self.metrics.cycles_since_last_adaptation = 0

        # Logging
        if self.config['logging']['log_all_adaptations']:
            logger.info(f"ADAPTAÇÃO APLICADA: {reason.value}")
            logger.info(f"Estado: {old_state.value} -> {new_state.value}")
            logger.info(f"Ajuste: {adjustment_percentage:+.1%}")

            for metric, old_val in old_thresholds.items():
                new_val = new_thresholds[metric]
                change = new_val - old_val
                logger.info(f"  {metric}: {old_val:.3f} -> {new_val:.3f} ({change:+.3f})")

        # Salvar histórico
        if self.config['logging']['save_adaptation_history']:
            self._save_adaptation_history()

        return new_thresholds

    def apply_gradual_return(self) -> Dict[str, float]:
        """
        Aplica retorno gradual aos valores calibrados

        Returns:
            Dict com os thresholds atualizados
        """
        if self.current_state == AdaptationState.CALIBRATED:
            return self.current_thresholds

        old_thresholds = self.current_thresholds.copy()
        return_step = self.config['gradual_return']['return_step_percentage']

        new_thresholds = {}
        for metric, current_value in self.current_thresholds.items():
            calibrated_value = self.calibrated_thresholds[metric]

            # Calcular direção e magnitude do retorno
            difference = calibrated_value - current_value
            step_adjustment = difference * return_step

            new_value = current_value + step_adjustment

            # Se estamos muito próximos do valor calibrado, usar o valor exato
            if abs(new_value - calibrated_value) < 0.001:
                new_value = calibrated_value

            new_thresholds[metric] = round(new_value, 3)

        # Verificar se retornamos completamente aos valores calibrados
        all_calibrated = all(
            abs(new_thresholds[metric] - self.calibrated_thresholds[metric]) < 0.001
            for metric in new_thresholds
        )

        new_state = AdaptationState.CALIBRATED if all_calibrated else AdaptationState.RETURNING

        # Criar registro
        record = AdaptationRecord(
            timestamp=datetime.now().isoformat(),
            reason=AdaptationReason.GRADUAL_RETURN,
            state_before=self.current_state,
            state_after=new_state,
            thresholds_before=old_thresholds,
            thresholds_after=new_thresholds,
            pass_rate_before=self.metrics.current_pass_rate,
            cycles_without_signals=self.metrics.cycles_without_signals,
            adjustment_percentage=return_step,
            notes="Retorno gradual aos valores calibrados"
        )

        # Atualizar estado
        self.current_thresholds = new_thresholds
        self.current_state = new_state
        self.adaptation_history.append(record)

        if new_state == AdaptationState.CALIBRATED:
            self.last_adaptation_time = datetime.now()
            self.metrics.cycles_since_last_adaptation = 0

        # Logging
        if self.config['logging']['log_all_adaptations']:
            logger.info(f"RETORNO GRADUAL: {return_step:.1%} em direção aos valores calibrados")
            logger.info(f"Estado: {record.state_before.value} -> {new_state.value}")

            if new_state == AdaptationState.CALIBRATED:
                logger.info("RETORNO COMPLETO: Valores calibrados restaurados!")

        # Salvar histórico
        if self.config['logging']['save_adaptation_history']:
            self._save_adaptation_history()

        return new_thresholds

    def process_cycle(self, pass_rate: float, assets_analyzed: int, signals_found: int) -> Dict[str, float]:
        """
        Processa um ciclo completo de análise e adaptação

        Args:
            pass_rate: Taxa de aprovação atual
            assets_analyzed: Número de ativos analisados
            signals_found: Número de sinais encontrados

        Returns:
            Dict com os thresholds atualizados (se houver mudança)
        """
        # Atualizar métricas
        self.update_metrics(pass_rate, assets_analyzed, signals_found)

        # 🚨 HOTFIX CRÍTICO: Detectar emergência de escala
        if self._detect_scale_emergency(pass_rate):
            logger.warning(f"🚨 EMERGÊNCIA DE ESCALA: {pass_rate:.1%} por {self.metrics.cycles_without_signals} ciclos")
            return self._emergency_scale_override(pass_rate)

        # Verificar se deve retornar aos valores calibrados
        if self.should_return_to_calibrated():
            return self.apply_gradual_return()

        # Verificar se deve adaptar
        should_adapt, reason, adjustment = self.should_adapt()
        if should_adapt:
            return self.apply_adaptation(reason, adjustment)

        return self.current_thresholds

    def _detect_scale_emergency(self, pass_rate: float) -> bool:
        """
        Detecta emergência de escala nos thresholds

        Critérios:
        - Taxa de aprovação < 5% por >= 3 ciclos
        - Momentum_min provavelmente fora de escala
        """
        return (pass_rate < 0.05 and
                self.metrics.cycles_without_signals >= 3)

    def _emergency_scale_override(self, pass_rate: float) -> Dict[str, float]:
        """
        Override de emergência para corrigir problemas de escala

        Aplica correções drásticas mas necessárias:
        - Momentum_min: Reduzir para escala realista (0.0003-0.002)
        - Consciousness/Coherence/Confidence: Reduzir significativamente
        """
        logger.warning("🚨 APLICANDO OVERRIDE DE EMERGÊNCIA - CORREÇÃO DE ESCALA")

        # Salvar estado anterior
        old_thresholds = self.current_thresholds.copy()

        # Correções drásticas mas necessárias
        emergency_thresholds = {
            # Correção crítica de momentum (escala estava 100x maior)
            'momentum_min': max(self.current_thresholds['momentum_min'] * 0.01, 0.0003),

            # Reduções significativas para permitir aprovações
            'consciousness': max(self.current_thresholds['consciousness'] - 0.08, 0.45),
            'coherence': max(self.current_thresholds['coherence'] - 0.08, 0.35),
            'confidence': max(self.current_thresholds['confidence'] - 0.06, 0.40),

            # Volume e métricas geométricas - reduções moderadas
            'volume_surge_min': max(self.current_thresholds['volume_surge_min'] * 0.7, 0.8),
            'spectral_phi_alignment_min': max(self.current_thresholds['spectral_phi_alignment_min'] * 0.8, 0.3),
            'golden_symmetry_min': max(self.current_thresholds['golden_symmetry_min'] * 0.8, 0.3)
        }

        # Atualizar thresholds
        self.current_thresholds.update(emergency_thresholds)
        self.current_state = AdaptationState.EMERGENCY

        # Log das mudanças críticas
        logger.warning("🔧 CORREÇÕES DE EMERGÊNCIA APLICADAS:")
        for metric, new_value in emergency_thresholds.items():
            old_value = old_thresholds[metric]
            change_pct = ((new_value - old_value) / old_value) * 100
            logger.warning(f"   {metric}: {old_value:.4f} → {new_value:.4f} ({change_pct:+.1f}%)")

        # Registrar adaptação
        self._record_adaptation(
            reason=AdaptationReason.EMERGENCY,
            old_thresholds=old_thresholds,
            new_thresholds=self.current_thresholds.copy(),
            pass_rate=pass_rate,
            adjustment_percentage=-50.0,  # Indicativo de redução drástica
            notes="Override de emergência - correção de escala crítica"
        )

        logger.warning("⚠️ SISTEMA EM MODO EMERGÊNCIA - Monitorar próximos ciclos")
        return self.current_thresholds

    def _save_adaptation_history(self):
        """Salva histórico de adaptações em arquivo"""
        try:
            history_file = Path(self.config['logging']['history_file'])

            # Preparar dados para serialização
            history_data = {
                'system_info': {
                    'calibrated_thresholds': self.calibrated_thresholds,
                    'current_state': self.current_state.value,
                    'last_update': datetime.now().isoformat()
                },
                'adaptations': [
                    {
                        **asdict(record),
                        'reason': record.reason.value,
                        'state_before': record.state_before.value,
                        'state_after': record.state_after.value
                    }
                    for record in self.adaptation_history
                ],
                'performance_history': self.performance_history[-100:]  # Últimos 100 registros
            }

            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Erro salvando histórico de adaptações: {e}")

    def get_status_report(self) -> Dict[str, Any]:
        """Retorna relatório completo do status do sistema"""
        return {
            'system_status': {
                'enabled': self.config['enabled'],
                'current_state': self.current_state.value,
                'time_since_last_adaptation_hours': self.metrics.time_since_last_adaptation,
                'cycles_since_last_adaptation': self.metrics.cycles_since_last_adaptation
            },
            'current_metrics': {
                'pass_rate': self.metrics.current_pass_rate,
                'cycles_without_signals': self.metrics.cycles_without_signals,
                'total_assets_analyzed': self.metrics.total_assets_analyzed,
                'consecutive_low_rate_cycles': self.metrics.consecutive_low_rate_cycles,
                'consecutive_high_rate_cycles': self.metrics.consecutive_high_rate_cycles
            },
            'thresholds': {
                'calibrated': self.calibrated_thresholds,
                'current': self.current_thresholds,
                'deviations': {
                    metric: {
                        'absolute': self.current_thresholds[metric] - self.calibrated_thresholds[metric],
                        'percentage': ((self.current_thresholds[metric] - self.calibrated_thresholds[metric]) / self.calibrated_thresholds[metric]) * 100
                    }
                    for metric in self.calibrated_thresholds
                }
            },
            'adaptation_history': {
                'total_adaptations': len(self.adaptation_history),
                'recent_adaptations': [asdict(record) for record in self.adaptation_history[-5:]]
            }
        }

    def reset_to_calibrated(self, reason: str = "Manual reset"):
        """Força retorno imediato aos valores calibrados"""
        old_thresholds = self.current_thresholds.copy()
        old_state = self.current_state

        self.current_thresholds = self.calibrated_thresholds.copy()
        self.current_state = AdaptationState.CALIBRATED

        # Criar registro
        record = AdaptationRecord(
            timestamp=datetime.now().isoformat(),
            reason=AdaptationReason.MANUAL,
            state_before=old_state,
            state_after=AdaptationState.CALIBRATED,
            thresholds_before=old_thresholds,
            thresholds_after=self.current_thresholds,
            pass_rate_before=self.metrics.current_pass_rate,
            cycles_without_signals=self.metrics.cycles_without_signals,
            adjustment_percentage=0.0,
            notes=reason
        )

        self.adaptation_history.append(record)
        self.last_adaptation_time = datetime.now()
        self.metrics.cycles_since_last_adaptation = 0

        logger.info(f"RESET MANUAL: Retorno forçado aos valores calibrados - {reason}")

        if self.config['logging']['save_adaptation_history']:
            self._save_adaptation_history()
