{"trades": [], "active_trades": [{"order_id": "11525938804", "trade_record": {"trade_id": "90671000-d17b-45ba-86d6-5bb0c67fc04a", "timestamp": "2025-07-22T14:16:11.311846", "symbol": "XRP/USDT", "direction": "buy", "entry_price": 3.5255, "target_price": 3.567806, "stop_price": 3.497296, "position_size_usd": 21, "quantum_metrics": {"consciousness": 0.9198553396681324, "coherence": 0.9973633535720128, "confidence": 0.8971635229045593, "momentum": 0.012643081401492097, "volume_surge": 1.690706373543525, "volatility": 0.0008788821426624563, "spread": 2.8364770954408435e-05, "price_action_strength": 0.0019855339668132512, "advanced_quality_score": 0.8232491475125165, "liquidity_score": 0.9914905687136775, "stability_score": 0.8947267071440254, "momentum_quality": 0.4550572325605968, "execution_quality": 0.9898885977901397, "predictive_score": 0.895}, "adaptive_system": {"adaptive_mode": "moderate", "approval_rate_current": 0, "mode_stability_counter": 1, "cycles_without_signals": 0}, "active_thresholds": {"consciousness": 0.693, "coherence": 0.6202, "confidence": 0.6166999999999999, "momentum_min": 0.005, "volume_surge_min": 0.595}, "result": {"outcome": "pending", "executed_price": 3.5255, "executed_quantity": 5.9, "pnl": 0.0, "fees_paid": 0, "execution_time": "2025-07-22T14:16:10.705794", "order_type": "market", "order_id": "11525938804", "close_price": null, "close_time": null}, "market_context": {"market_conditions": "challenging", "volatility_regime": "low", "volume_24h": 253798935.5, "spread": 2.8367989560513726e-05, "price_at_execution": 3.5255}, "filters_applied": {"method_used": "standard", "filters_passed": [], "filters_failed": [], "approval_confidence": 0.8232491475125165, "empirical_analysis": {}, "quality_score": 0.8232491475125165}, "metadata": {"system_version": "QUALIA_EMPIRICAL_v2.1", "recorded_at": "2025-07-22T14:16:11.311846", "status": "active", "updated_at": "2025-07-22T14:16:11.311846"}}}], "last_updated": "2025-07-22T14:16:11.311846", "total_trades": 0, "active_count": 1, "system_version": "QUALIA_EMPIRICAL_v2.1"}