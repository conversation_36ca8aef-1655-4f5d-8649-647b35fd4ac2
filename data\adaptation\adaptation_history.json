{"system_info": {"calibrated_thresholds": {"consciousness": 0.854, "coherence": 0.82, "confidence": 0.719, "volume_surge_min": 0.856, "momentum_min": 0.008, "spectral_phi_alignment_min": 0.538, "golden_symmetry_min": 0.634}, "current_state": "returning", "last_update": "2025-07-22T01:49:00.658243"}, "adaptations": [{"timestamp": "2025-07-22T01:49:00.629324", "reason": "low_pass_rate", "state_before": "calibrated", "state_after": "restrictive", "thresholds_before": {"consciousness": 0.854, "coherence": 0.82, "confidence": 0.719, "volume_surge_min": 0.856, "momentum_min": 0.008, "spectral_phi_alignment_min": 0.538, "golden_symmetry_min": 0.634}, "thresholds_after": {"consciousness": 0.726, "coherence": 0.697, "confidence": 0.611, "volume_surge_min": 0.728, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.457, "golden_symmetry_min": 0.539}, "pass_rate_before": 0.01, "cycles_without_signals": 0, "adjustment_percentage": -0.15, "notes": "Adaptação automática: low_pass_rate"}, {"timestamp": "2025-07-22T01:49:00.631116", "reason": "manual", "state_before": "restrictive", "state_after": "calibrated", "thresholds_before": {"consciousness": 0.726, "coherence": 0.697, "confidence": 0.611, "volume_surge_min": 0.728, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.457, "golden_symmetry_min": 0.539}, "thresholds_after": {"consciousness": 0.854, "coherence": 0.82, "confidence": 0.719, "volume_surge_min": 0.856, "momentum_min": 0.008, "spectral_phi_alignment_min": 0.538, "golden_symmetry_min": 0.634}, "pass_rate_before": 0.01, "cycles_without_signals": 0, "adjustment_percentage": 0.0, "notes": "Demo 2"}, {"timestamp": "2025-07-22T01:49:00.636385", "reason": "high_pass_rate", "state_before": "calibrated", "state_after": "permissive", "thresholds_before": {"consciousness": 0.854, "coherence": 0.82, "confidence": 0.719, "volume_surge_min": 0.856, "momentum_min": 0.008, "spectral_phi_alignment_min": 0.538, "golden_symmetry_min": 0.634}, "thresholds_after": {"consciousness": 0.95, "coherence": 0.943, "confidence": 0.827, "volume_surge_min": 0.95, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.619, "golden_symmetry_min": 0.729}, "pass_rate_before": 0.45, "cycles_without_signals": 0, "adjustment_percentage": 0.15, "notes": "Adaptação automática: high_pass_rate"}, {"timestamp": "2025-07-22T01:49:00.641453", "reason": "manual", "state_before": "permissive", "state_after": "calibrated", "thresholds_before": {"consciousness": 0.95, "coherence": 0.943, "confidence": 0.827, "volume_surge_min": 0.95, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.619, "golden_symmetry_min": 0.729}, "thresholds_after": {"consciousness": 0.854, "coherence": 0.82, "confidence": 0.719, "volume_surge_min": 0.856, "momentum_min": 0.008, "spectral_phi_alignment_min": 0.538, "golden_symmetry_min": 0.634}, "pass_rate_before": 0.45, "cycles_without_signals": 0, "adjustment_percentage": 0.0, "notes": "Demo 3"}, {"timestamp": "2025-07-22T01:49:00.646983", "reason": "emergency", "state_before": "calibrated", "state_after": "emergency", "thresholds_before": {"consciousness": 0.854, "coherence": 0.82, "confidence": 0.719, "volume_surge_min": 0.856, "momentum_min": 0.008, "spectral_phi_alignment_min": 0.538, "golden_symmetry_min": 0.634}, "thresholds_after": {"consciousness": 0.683, "coherence": 0.656, "confidence": 0.575, "volume_surge_min": 0.685, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.43, "golden_symmetry_min": 0.507}, "pass_rate_before": 0.0, "cycles_without_signals": 3, "adjustment_percentage": -0.25, "notes": "Adaptação automática: emergency"}, {"timestamp": "2025-07-22T01:49:00.655319", "reason": "gradual_return", "state_before": "emergency", "state_after": "returning", "thresholds_before": {"consciousness": 0.683, "coherence": 0.656, "confidence": 0.575, "volume_surge_min": 0.685, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.43, "golden_symmetry_min": 0.507}, "thresholds_after": {"consciousness": 0.692, "coherence": 0.664, "confidence": 0.582, "volume_surge_min": 0.694, "momentum_min": 0.285, "spectral_phi_alignment_min": 0.435, "golden_symmetry_min": 0.513}, "pass_rate_before": 0.18, "cycles_without_signals": 0, "adjustment_percentage": 0.05, "notes": "Retorno gradual aos valores calibrados"}], "performance_history": [{"timestamp": "2025-07-22T01:49:00.629324", "pass_rate": 0.01, "assets_analyzed": 100, "signals_found": 1, "state": "calibrated"}, {"timestamp": "2025-07-22T01:49:00.629324", "pass_rate": 0.01, "assets_analyzed": 100, "signals_found": 1, "state": "calibrated"}, {"timestamp": "2025-07-22T01:49:00.629324", "pass_rate": 0.01, "assets_analyzed": 100, "signals_found": 1, "state": "calibrated"}, {"timestamp": "2025-07-22T01:49:00.635385", "pass_rate": 0.45, "assets_analyzed": 100, "signals_found": 45, "state": "calibrated"}, {"timestamp": "2025-07-22T01:49:00.635385", "pass_rate": 0.45, "assets_analyzed": 100, "signals_found": 45, "state": "calibrated"}, {"timestamp": "2025-07-22T01:49:00.636385", "pass_rate": 0.45, "assets_analyzed": 100, "signals_found": 45, "state": "calibrated"}, {"timestamp": "2025-07-22T01:49:00.641453", "pass_rate": 0.0, "assets_analyzed": 100, "signals_found": 0, "state": "calibrated"}, {"timestamp": "2025-07-22T01:49:00.641453", "pass_rate": 0.0, "assets_analyzed": 100, "signals_found": 0, "state": "calibrated"}, {"timestamp": "2025-07-22T01:49:00.646767", "pass_rate": 0.0, "assets_analyzed": 100, "signals_found": 0, "state": "calibrated"}, {"timestamp": "2025-07-22T01:49:00.654366", "pass_rate": 0.18, "assets_analyzed": 100, "signals_found": 18, "state": "emergency"}, {"timestamp": "2025-07-22T01:49:00.655319", "pass_rate": 0.18, "assets_analyzed": 100, "signals_found": 18, "state": "emergency"}, {"timestamp": "2025-07-22T01:49:00.655319", "pass_rate": 0.18, "assets_analyzed": 100, "signals_found": 18, "state": "emergency"}, {"timestamp": "2025-07-22T01:49:00.655319", "pass_rate": 0.18, "assets_analyzed": 100, "signals_found": 18, "state": "emergency"}, {"timestamp": "2025-07-22T01:49:00.655319", "pass_rate": 0.18, "assets_analyzed": 100, "signals_found": 18, "state": "emergency"}, {"timestamp": "2025-07-22T01:49:00.655319", "pass_rate": 0.18, "assets_analyzed": 100, "signals_found": 18, "state": "emergency"}, {"timestamp": "2025-07-22T01:49:00.655319", "pass_rate": 0.18, "assets_analyzed": 100, "signals_found": 18, "state": "emergency"}, {"timestamp": "2025-07-22T01:49:00.655319", "pass_rate": 0.18, "assets_analyzed": 100, "signals_found": 18, "state": "emergency"}, {"timestamp": "2025-07-22T01:49:00.655319", "pass_rate": 0.18, "assets_analyzed": 100, "signals_found": 18, "state": "emergency"}, {"timestamp": "2025-07-22T01:49:00.655319", "pass_rate": 0.18, "assets_analyzed": 100, "signals_found": 18, "state": "emergency"}]}