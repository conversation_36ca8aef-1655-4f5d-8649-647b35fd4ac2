{"system_info": {"calibrated_thresholds": {"consciousness": 0.92, "coherence": 0.97, "confidence": 0.798, "volume_surge_min": 0.689, "momentum_min": 0.011, "spectral_phi_alignment_min": 0.553, "golden_symmetry_min": 0.69}, "current_state": "emergency", "last_update": "2025-07-22T04:17:18.170934"}, "adaptations": [{"timestamp": "2025-07-22T04:03:09.378540", "reason": "emergency", "state_before": "calibrated", "state_after": "emergency", "thresholds_before": {"consciousness": 0.92, "coherence": 0.97, "confidence": 0.798, "volume_surge_min": 0.689, "momentum_min": 0.011, "spectral_phi_alignment_min": 0.553, "golden_symmetry_min": 0.69}, "thresholds_after": {"consciousness": 0.736, "coherence": 0.776, "confidence": 0.638, "volume_surge_min": 0.551, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.442, "golden_symmetry_min": 0.552}, "pass_rate_before": 0.0, "cycles_without_signals": 3, "adjustment_percentage": -0.25, "notes": "Adaptação automática: emergency"}, {"timestamp": "2025-07-22T04:06:41.019372", "reason": "emergency", "state_before": "emergency", "state_after": "emergency", "thresholds_before": {"consciousness": 0.736, "coherence": 0.776, "confidence": 0.638, "volume_surge_min": 0.551, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.442, "golden_symmetry_min": 0.552}, "thresholds_after": {"consciousness": 0.736, "coherence": 0.776, "confidence": 0.638, "volume_surge_min": 0.551, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.442, "golden_symmetry_min": 0.552}, "pass_rate_before": 0.0, "cycles_without_signals": 6, "adjustment_percentage": -0.25, "notes": "Adaptação automática: emergency"}, {"timestamp": "2025-07-22T04:10:12.889628", "reason": "emergency", "state_before": "emergency", "state_after": "emergency", "thresholds_before": {"consciousness": 0.736, "coherence": 0.776, "confidence": 0.638, "volume_surge_min": 0.551, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.442, "golden_symmetry_min": 0.552}, "thresholds_after": {"consciousness": 0.736, "coherence": 0.776, "confidence": 0.638, "volume_surge_min": 0.551, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.442, "golden_symmetry_min": 0.552}, "pass_rate_before": 0.0, "cycles_without_signals": 9, "adjustment_percentage": -0.25, "notes": "Adaptação automática: emergency"}, {"timestamp": "2025-07-22T04:13:44.385376", "reason": "emergency", "state_before": "emergency", "state_after": "emergency", "thresholds_before": {"consciousness": 0.736, "coherence": 0.776, "confidence": 0.638, "volume_surge_min": 0.551, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.442, "golden_symmetry_min": 0.552}, "thresholds_after": {"consciousness": 0.736, "coherence": 0.776, "confidence": 0.638, "volume_surge_min": 0.551, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.442, "golden_symmetry_min": 0.552}, "pass_rate_before": 0.0, "cycles_without_signals": 12, "adjustment_percentage": -0.25, "notes": "Adaptação automática: emergency"}, {"timestamp": "2025-07-22T04:17:18.155307", "reason": "emergency", "state_before": "emergency", "state_after": "emergency", "thresholds_before": {"consciousness": 0.736, "coherence": 0.776, "confidence": 0.638, "volume_surge_min": 0.551, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.442, "golden_symmetry_min": 0.552}, "thresholds_after": {"consciousness": 0.736, "coherence": 0.776, "confidence": 0.638, "volume_surge_min": 0.551, "momentum_min": 0.3, "spectral_phi_alignment_min": 0.442, "golden_symmetry_min": 0.552}, "pass_rate_before": 0.0, "cycles_without_signals": 15, "adjustment_percentage": -0.25, "notes": "Adaptação automática: emergency"}], "performance_history": [{"timestamp": "2025-07-22T04:00:48.420211", "pass_rate": 0.0, "assets_analyzed": 12, "signals_found": 0, "state": "calibrated"}, {"timestamp": "2025-07-22T04:01:58.874072", "pass_rate": 0.0, "assets_analyzed": 24, "signals_found": 0, "state": "calibrated"}, {"timestamp": "2025-07-22T04:03:09.378540", "pass_rate": 0.0, "assets_analyzed": 36, "signals_found": 0, "state": "calibrated"}, {"timestamp": "2025-07-22T04:04:19.873780", "pass_rate": 0.0, "assets_analyzed": 48, "signals_found": 0, "state": "emergency"}, {"timestamp": "2025-07-22T04:05:30.471814", "pass_rate": 0.0, "assets_analyzed": 60, "signals_found": 0, "state": "emergency"}, {"timestamp": "2025-07-22T04:06:41.019372", "pass_rate": 0.0, "assets_analyzed": 72, "signals_found": 0, "state": "emergency"}, {"timestamp": "2025-07-22T04:07:51.591388", "pass_rate": 0.0, "assets_analyzed": 84, "signals_found": 0, "state": "emergency"}, {"timestamp": "2025-07-22T04:09:02.074986", "pass_rate": 0.0, "assets_analyzed": 96, "signals_found": 0, "state": "emergency"}, {"timestamp": "2025-07-22T04:10:12.889628", "pass_rate": 0.0, "assets_analyzed": 108, "signals_found": 0, "state": "emergency"}, {"timestamp": "2025-07-22T04:11:23.369412", "pass_rate": 0.0, "assets_analyzed": 120, "signals_found": 0, "state": "emergency"}, {"timestamp": "2025-07-22T04:12:33.925496", "pass_rate": 0.0, "assets_analyzed": 132, "signals_found": 0, "state": "emergency"}, {"timestamp": "2025-07-22T04:13:44.385376", "pass_rate": 0.0, "assets_analyzed": 144, "signals_found": 0, "state": "emergency"}, {"timestamp": "2025-07-22T04:14:54.820663", "pass_rate": 0.0, "assets_analyzed": 156, "signals_found": 0, "state": "emergency"}, {"timestamp": "2025-07-22T04:16:07.693706", "pass_rate": 0.0, "assets_analyzed": 168, "signals_found": 0, "state": "emergency"}, {"timestamp": "2025-07-22T04:17:18.155307", "pass_rate": 0.0, "assets_analyzed": 180, "signals_found": 0, "state": "emergency"}]}