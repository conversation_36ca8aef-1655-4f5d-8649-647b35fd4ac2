#!/usr/bin/env python3
"""
Demonstração do Sistema de Adaptação Inteligente
Mostra o sistema em ação com parâmetros mais agressivos para demonstração
"""

import sys
import json
from pathlib import Path

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from qualia.config_manager import get_config_manager
from qualia.intelligent_adaptation_system import IntelligentAdaptationSystem, AdaptationState

def demo_intelligent_adaptation():
    """Demonstração do sistema com parâmetros mais agressivos"""
    
    print("DEMONSTRAÇÃO DO SISTEMA DE ADAPTAÇÃO INTELIGENTE")
    print("=" * 70)
    
    # Carregar configuração
    config_manager = get_config_manager('config/qualia_config.yaml')
    
    # Modificar configurações para demonstração (mais agressivo)
    config_manager.config['quantum_thresholds']['intelligent_adaptation']['advanced']['require_confirmation_cycles'] = 1
    config_manager.config['quantum_thresholds']['intelligent_adaptation']['advanced']['adaptation_cooldown_cycles'] = 3
    config_manager.config['quantum_thresholds']['intelligent_adaptation']['activation_criteria']['max_cycles_without_signals'] = 3
    
    # Thresholds calibrados
    calibrated_thresholds = {
        'consciousness': 0.854,
        'coherence': 0.820,
        'confidence': 0.719,
        'volume_surge_min': 0.856,
        'momentum_min': 0.008,
        'spectral_phi_alignment_min': 0.538,
        'golden_symmetry_min': 0.634
    }
    
    # Inicializar sistema
    adaptation_system = IntelligentAdaptationSystem(
        config_manager=config_manager,
        calibrated_thresholds=calibrated_thresholds
    )
    
    print(f"Sistema inicializado: {adaptation_system.current_state.value}")
    print(f"Parâmetros ajustados para demonstração:")
    print(f"  - Confirmação: 1 ciclo (vs 3 padrão)")
    print(f"  - Cooldown: 3 ciclos (vs 20 padrão)")
    print(f"  - Max ciclos sem sinais: 3 (vs 5 padrão)")
    print()
    
    # DEMONSTRAÇÃO 1: Taxa muito baixa -> Adaptação restritiva
    print("DEMO 1: TAXA MUITO BAIXA (1%) - SISTEMA MUITO RESTRITIVO")
    print("-" * 50)
    
    for cycle in range(1, 8):
        pass_rate = 0.01  # 1% - muito baixo
        assets_analyzed = 100
        signals_found = 1
        
        old_consciousness = adaptation_system.current_thresholds['consciousness']
        updated_thresholds = adaptation_system.process_cycle(pass_rate, assets_analyzed, signals_found)
        new_consciousness = updated_thresholds['consciousness']
        
        print(f"Ciclo {cycle}:")
        print(f"  Taxa: {pass_rate:.1%} | Estado: {adaptation_system.current_state.value}")
        print(f"  Consciousness: {old_consciousness:.3f} -> {new_consciousness:.3f}")
        
        if old_consciousness != new_consciousness:
            change_pct = ((new_consciousness - old_consciousness) / old_consciousness) * 100
            print(f"  ADAPTAÇÃO! Mudança: {change_pct:+.1f}%")
        
        print(f"  Ciclos sem sinais: {adaptation_system.metrics.cycles_without_signals}")
        print()
        
        # Parar se adaptou
        if adaptation_system.current_state != AdaptationState.CALIBRATED:
            break
    
    print("RESULTADO: Sistema adaptou para ser MENOS RESTRITIVO")
    print(f"Consciousness: {calibrated_thresholds['consciousness']:.3f} -> {adaptation_system.current_thresholds['consciousness']:.3f}")
    print()
    
    # DEMONSTRAÇÃO 2: Taxa muito alta -> Adaptação permissiva
    print("DEMO 2: TAXA MUITO ALTA (45%) - SISTEMA MUITO PERMISSIVO")
    print("-" * 50)
    
    # Reset para valores calibrados
    adaptation_system.reset_to_calibrated("Demo 2")
    
    for cycle in range(1, 8):
        pass_rate = 0.45  # 45% - muito alto
        assets_analyzed = 100
        signals_found = 45
        
        old_consciousness = adaptation_system.current_thresholds['consciousness']
        updated_thresholds = adaptation_system.process_cycle(pass_rate, assets_analyzed, signals_found)
        new_consciousness = updated_thresholds['consciousness']
        
        print(f"Ciclo {cycle}:")
        print(f"  Taxa: {pass_rate:.1%} | Estado: {adaptation_system.current_state.value}")
        print(f"  Consciousness: {old_consciousness:.3f} -> {new_consciousness:.3f}")
        
        if old_consciousness != new_consciousness:
            change_pct = ((new_consciousness - old_consciousness) / old_consciousness) * 100
            print(f"  ADAPTAÇÃO! Mudança: {change_pct:+.1f}%")
        
        print()
        
        # Parar se adaptou
        if adaptation_system.current_state != AdaptationState.CALIBRATED:
            break
    
    print("RESULTADO: Sistema adaptou para ser MAIS RESTRITIVO")
    print(f"Consciousness: {calibrated_thresholds['consciousness']:.3f} -> {adaptation_system.current_thresholds['consciousness']:.3f}")
    print()
    
    # DEMONSTRAÇÃO 3: Sem sinais -> Modo emergência
    print("DEMO 3: SEM SINAIS POR VÁRIOS CICLOS - MODO EMERGÊNCIA")
    print("-" * 50)
    
    # Reset para valores calibrados
    adaptation_system.reset_to_calibrated("Demo 3")
    
    for cycle in range(1, 6):
        pass_rate = 0.0  # 0% - sem sinais
        assets_analyzed = 100
        signals_found = 0
        
        old_consciousness = adaptation_system.current_thresholds['consciousness']
        updated_thresholds = adaptation_system.process_cycle(pass_rate, assets_analyzed, signals_found)
        new_consciousness = updated_thresholds['consciousness']
        
        print(f"Ciclo {cycle}:")
        print(f"  Taxa: {pass_rate:.1%} | Estado: {adaptation_system.current_state.value}")
        print(f"  Consciousness: {old_consciousness:.3f} -> {new_consciousness:.3f}")
        print(f"  Ciclos sem sinais: {adaptation_system.metrics.cycles_without_signals}")
        
        if old_consciousness != new_consciousness:
            change_pct = ((new_consciousness - old_consciousness) / old_consciousness) * 100
            print(f"  ADAPTAÇÃO! Mudança: {change_pct:+.1f}%")
        
        print()
        
        # Parar se adaptou
        if adaptation_system.current_state != AdaptationState.CALIBRATED:
            break
    
    print("RESULTADO: Sistema entrou em modo de adaptação por falta de sinais")
    print(f"Consciousness: {calibrated_thresholds['consciousness']:.3f} -> {adaptation_system.current_thresholds['consciousness']:.3f}")
    print()
    
    # DEMONSTRAÇÃO 4: Retorno gradual
    print("DEMO 4: RETORNO GRADUAL AOS VALORES CALIBRADOS")
    print("-" * 50)
    
    # Simular passagem de tempo para trigger de retorno
    adaptation_system.metrics.cycles_since_last_adaptation = 60
    
    print("Simulando retorno gradual...")
    for cycle in range(1, 15):
        pass_rate = 0.18  # 18% - taxa ideal
        assets_analyzed = 100
        signals_found = 18
        
        old_consciousness = adaptation_system.current_thresholds['consciousness']
        updated_thresholds = adaptation_system.process_cycle(pass_rate, assets_analyzed, signals_found)
        new_consciousness = updated_thresholds['consciousness']
        
        if cycle % 3 == 0:  # Mostrar a cada 3 ciclos
            print(f"Ciclo {cycle}:")
            print(f"  Estado: {adaptation_system.current_state.value}")
            print(f"  Consciousness: {new_consciousness:.3f}")
            
            if adaptation_system.current_state == AdaptationState.CALIBRATED:
                print("  RETORNO COMPLETO!")
                break
            else:
                distance = abs(new_consciousness - calibrated_thresholds['consciousness'])
                print(f"  Distância do calibrado: {distance:.3f}")
            print()
    
    # RELATÓRIO FINAL
    print("=" * 70)
    print("RELATÓRIO FINAL DA DEMONSTRAÇÃO")
    print("=" * 70)
    
    status_report = adaptation_system.get_status_report()
    
    print(f"Estado final: {status_report['system_status']['current_state']}")
    print(f"Total de adaptações realizadas: {status_report['adaptation_history']['total_adaptations']}")
    print()
    
    print("COMPARAÇÃO FINAL - THRESHOLDS:")
    print("-" * 30)
    for metric in ['consciousness', 'coherence', 'confidence']:
        calibrated = status_report['thresholds']['calibrated'][metric]
        current = status_report['thresholds']['current'][metric]
        deviation = status_report['thresholds']['deviations'][metric]['percentage']
        
        print(f"{metric}:")
        print(f"  Calibrado: {calibrated:.3f}")
        print(f"  Atual: {current:.3f}")
        print(f"  Desvio: {deviation:+.1f}%")
    
    print("\nHISTÓRICO DE ADAPTAÇÕES:")
    print("-" * 25)
    for i, adaptation in enumerate(status_report['adaptation_history']['recent_adaptations'][-5:], 1):
        print(f"{i}. {adaptation['reason']} -> {adaptation['state_after']}")
        print(f"   Ajuste: {adaptation['adjustment_percentage']:+.1%}")
    
    print("\n" + "=" * 70)
    print("DEMONSTRAÇÃO CONCLUÍDA!")
    print("O sistema mostrou capacidade de:")
    print("✓ Detectar condições que requerem adaptação")
    print("✓ Aplicar ajustes controlados baseados nos valores calibrados")
    print("✓ Retornar gradualmente aos valores calibrados")
    print("✓ Manter limites de segurança")
    print("✓ Registrar todas as mudanças para auditoria")
    print("=" * 70)

if __name__ == "__main__":
    try:
        demo_intelligent_adaptation()
    except Exception as e:
        print(f"\nERRO durante demonstração: {e}")
        import traceback
        traceback.print_exc()
