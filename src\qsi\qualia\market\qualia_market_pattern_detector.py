"""
Detector de Padrões Quânticos Multiescala para Mercados.

Este módulo implementa um sistema avançado de detecção de padrões que utiliza
a capacidade do QUALIA de reconhecer correlações não-lineares e padrões emergentes
em múltiplas escalas temporais nos dados de mercado.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from datadog import DogStatsd

from ..memory.event_bus import SimpleEventBus
from .event_bus import MARKET_PATTERN_EVENT, MarketPatternDetected
from ..config.feature_flags import feature_toggle

from ..config.market_pattern_config import MarketPatternConfig
from ..config.settings import market_metrics_enabled
from ..utils.logger import get_logger
from ..utils.timeframe import timeframe_to_minutes


class QUALIAMarketPatternDetector:
    """
    Detector de padrões quânticos multiescala usando a capacidade de reconhecimento de padrões do QUALIA.

    Este detector analisa dados de mercado em múltiplas escalas temporais, utilizando
    as propriedades quânticas para identificar padrões emergentes e correlações complexas
    que não são facilmente visíveis em métodos de análise tradicionais.
    """

    def __init__(
        self,
        quantum_metrics_calculator: Any,
        config: Optional[MarketPatternConfig] = None,
        statsd_client: Optional[DogStatsd] = None,
        event_bus: Optional[SimpleEventBus] = None,
    ) -> None:
        """Inicializa o detector de padrões quânticos multiescala.

        Parameters
        ----------
        quantum_metrics_calculator:
            Instância utilizada para o cálculo das métricas quânticas.
        config:
            Configurações opcionais do detector. Quando ``None`` utiliza valores
            carregados das variáveis de ambiente.
        statsd_client:
            Cliente opcional para emissão de métricas ``DogStatsd``.
        event_bus:
            Instância opcional do ``SimpleEventBus`` para publicação de eventos.
        """
        cfg = config or MarketPatternConfig()
        self.calculator = quantum_metrics_calculator
        self.timeframes = cfg.timeframes
        self.pattern_registry: Dict[str, Any] = {}
        self.detected_patterns_history: List[Dict[str, Any]] = []
        self.cross_scale_correlations: Dict[str, Any] = {}
        self.logger = get_logger(__name__)
        self.event_bus = event_bus
        if statsd_client is not None or market_metrics_enabled:
            self.statsd: Optional[DogStatsd] = statsd_client or DogStatsd()
        else:
            self.statsd = None

    def detect_patterns(
        self,
        market_data: Dict[str, Any],
        timeframe_data: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Detecta padrões emergentes usando a capacidade do QUALIA.

        Args:
            market_data: Dados de mercado no timeframe principal
            timeframe_data: Dicionário opcional com dados para cada timeframe

        Returns:
            Dicionário com padrões detectados e sinais de trading
        """
        patterns: Dict[str, List[Dict[str, Any]]] = {}
        timestamp = datetime.now(timezone.utc).isoformat()
        start = datetime.now(timezone.utc).timestamp()

        # Se não houver dados específicos por timeframe, usar o principal para
        # todos
        if not timeframe_data:
            timeframe_data = {tf: market_data for tf in self.timeframes}

        # Para cada timeframe, identificar padrões emergentes
        for tf in self.timeframes:
            if tf in timeframe_data:
                tf_market_data = timeframe_data[tf]

                # Calcular métricas quânticas específicas para este timeframe
                tf_metrics = self.calculator.calculate_metrics(
                    tf_market_data, timeframe=tf
                )

                # Extrair indicadores de padrões emergentes
                emergent_patterns = self._extract_emergent_patterns(tf_metrics, tf)
                patterns[tf] = emergent_patterns

                # Registrar padrões detectados
                if emergent_patterns:
                    self.pattern_registry[f"{timestamp}_{tf}"] = {
                        "timeframe": tf,
                        "timestamp": timestamp,
                        "patterns": emergent_patterns,
                        "metrics": tf_metrics,
                    }
                    if self.event_bus and feature_toggle("market_module"):
                        vector = [
                            tf_metrics.get("quantum_metrics", {}).get("coherence", 0.0),
                            tf_metrics.get("quantum_metrics", {}).get("entropy", 0.0),
                        ]
                        for pattern in emergent_patterns:
                            self.event_bus.publish(
                                MARKET_PATTERN_EVENT,
                                MarketPatternDetected(vector=vector, metadata=pattern),
                            )

        # Sintetizar padrões multi-timeframe
        cross_scale_patterns = self._synthesize_cross_scale_patterns(patterns)

        # Avaliar relevância dos padrões para trading
        trading_signals = self._evaluate_pattern_trading_relevance(
            cross_scale_patterns, patterns
        )

        # Atualizar histórico
        detection_record = {
            "timestamp": timestamp,
            "patterns_by_timeframe": patterns,
            "cross_scale_patterns": cross_scale_patterns,
            "trading_signals": trading_signals,
        }
        self.detected_patterns_history.append(detection_record)

        # Limitar tamanho do histórico
        if len(self.detected_patterns_history) > 1000:
            self.detected_patterns_history.pop(0)

        # Construir resposta
        response = {
            "timestamp": timestamp,
            "patterns_by_timeframe": patterns,
            "cross_scale_patterns": cross_scale_patterns,
            "trading_signals": trading_signals,
            "confidence": self._calculate_overall_confidence(
                patterns, cross_scale_patterns
            ),
        }

        elapsed_ms = int((datetime.now(timezone.utc).timestamp() - start) * 1000)
        self.logger.info("%s padrões em %d ms", len(cross_scale_patterns), elapsed_ms)
        if self.statsd is not None:
            self.statsd.gauge(
                "market_pattern_detector.cross_scale_patterns",
                len(cross_scale_patterns),
            )
            self.statsd.timing("market_pattern_detector.detect_ms", elapsed_ms)

        return response

    def _extract_emergent_patterns(
        self, quantum_metrics: Dict[str, Any], timeframe: str
    ) -> List[Dict[str, Any]]:
        """
        Identifica padrões emergentes a partir das métricas quânticas.

        Args:
            quantum_metrics: Métricas quânticas calculadas para um timeframe
            timeframe: Timeframe analisado

        Returns:
            Lista de padrões emergentes detectados
        """
        patterns = []

        # Extrair métricas relevantes
        try:
            coherence = quantum_metrics.get("quantum_metrics", {}).get("coherence", 0.0)
            entropy = quantum_metrics.get("quantum_metrics", {}).get("entropy", 0.0)
            _pattern_recognition_rate = quantum_metrics.get("quantum_metrics", {}).get(
                "pattern_recognition_rate", 0.0
            )

            # Métricas adicionais, se disponíveis
            _qualia_universe_metrics = quantum_metrics.get("quantum_metrics", {}).get(
                "qualia_universe_metrics", {}
            )
            qast_result = quantum_metrics.get("quantum_metrics", {}).get(
                "qast_result", {}
            )
        except (KeyError, TypeError, AttributeError):
            # Se não conseguir extrair métricas, retornar lista vazia
            return patterns

        # Detector de Tendência Emergente
        if coherence > 0.6 and entropy < 0.4:
            # Alta coerência + baixa entropia = potencial tendência forte
            patterns.append(
                {
                    "type": "emerging_trend",
                    "confidence": coherence * (1 - entropy),
                    "description": f"Tendência emergente detectada no timeframe {timeframe}",
                    "attributes": {
                        "strength": coherence * (1 - entropy),
                        "coherence": coherence,
                        "entropy": entropy,
                    },
                }
            )

        # Detector de Reversão
        if coherence > 0.5 and entropy > 0.7:
            # Alta coerência + alta entropia = potencial reversão
            patterns.append(
                {
                    "type": "potential_reversal",
                    "confidence": coherence * 0.8,
                    "description": f"Potencial reversão no timeframe {timeframe}",
                    "attributes": {
                        "strength": coherence * 0.8,
                        "instability": entropy,
                        "coherence": coherence,
                    },
                }
            )

        # Detector de Consolidação/Range
        if coherence < 0.3 and entropy < 0.3:
            # Baixa coerência + baixa entropia = consolidação/range
            patterns.append(
                {
                    "type": "consolidation",
                    "confidence": 0.7 * (1 - coherence) * (1 - entropy),
                    "description": f"Padrão de consolidação no timeframe {timeframe}",
                    "attributes": {
                        "strength": 0.7 * (1 - coherence) * (1 - entropy),
                        "coherence": coherence,
                        "entropy": entropy,
                    },
                }
            )

        # Detector de Volatilidade Explosiva
        if entropy > 0.8 and coherence > 0.4:
            # Alta entropia + coerência moderada = volatilidade explosiva
            # potencial
            patterns.append(
                {
                    "type": "explosive_volatility",
                    "confidence": entropy * coherence,
                    "description": f"Volatilidade explosiva potencial no timeframe {timeframe}",
                    "attributes": {
                        "strength": entropy * coherence,
                        "volatility": entropy,
                        "direction_clarity": coherence,
                    },
                }
            )

        # Detector de Esgotamento
        if coherence < 0.2 and entropy > 0.7:
            # Baixa coerência + alta entropia = potencial esgotamento
            patterns.append(
                {
                    "type": "exhaustion",
                    "confidence": (1 - coherence) * entropy * 0.8,
                    "description": f"Padrão de esgotamento no timeframe {timeframe}",
                    "attributes": {
                        "strength": (1 - coherence) * entropy * 0.8,
                        "coherence": coherence,
                        "entropy": entropy,
                    },
                }
            )

        # Detector baseado em QAST (se disponível)
        if qast_result and isinstance(qast_result, dict):
            qast_coherence = qast_result.get("coherence", 0.0)
            qast_patterns = qast_result.get("patterns_detected", 0)
            qast_reflection = qast_result.get("self_reflection_depth", 0.0)

            if qast_patterns > 0 and qast_coherence > 0.4:
                patterns.append(
                    {
                        "type": "qast_identified_pattern",
                        "confidence": qast_coherence * (qast_reflection + 0.5),
                        "description": f"Padrão identificado pelo ciclo QAST no timeframe {timeframe}",
                        "attributes": {
                            "strength": qast_coherence * 0.9,
                            "patterns_count": qast_patterns,
                            "coherence": qast_coherence,
                            "reflection_depth": qast_reflection,
                        },
                    }
                )

        return patterns

    def _synthesize_cross_scale_patterns(
        self, patterns_by_timeframe: Dict[str, List[Dict[str, Any]]]
    ) -> List[Dict[str, Any]]:
        """
        Sintetiza padrões encontrados em diferentes timeframes em padrões multiescala.

        Args:
            patterns_by_timeframe: Dicionário com padrões por timeframe

        Returns:
            Lista de padrões multiescala
        """
        cross_scale_patterns = []

        # Agrupar padrões por tipo
        patterns_by_type = {}
        for tf, tf_patterns in patterns_by_timeframe.items():
            for pattern in tf_patterns:
                pattern_type = pattern["type"]
                if pattern_type not in patterns_by_type:
                    patterns_by_type[pattern_type] = []

                # Adicionar o timeframe ao padrão para referência
                pattern_with_tf = pattern.copy()
                pattern_with_tf["timeframe"] = tf
                patterns_by_type[pattern_type].append(pattern_with_tf)

        # Timeframes ordenados por tamanho (do menor para o maior)
        ordered_timeframes = self._order_timeframes(self.timeframes)

        # 1. Detecção de Tendências Multi-Timeframe
        if "emerging_trend" in patterns_by_type:
            trend_patterns = patterns_by_type["emerging_trend"]

            # Verificar alinhamento de tendências em múltiplos timeframes
            if len(trend_patterns) >= 2:
                # Calcular confiança média
                avg_confidence = sum(p["confidence"] for p in trend_patterns) / len(
                    trend_patterns
                )

                # Verificar se temos timeframes consecutivos
                timeframes_with_trend = [p["timeframe"] for p in trend_patterns]
                consecutive_count = self._count_consecutive_timeframes(
                    timeframes_with_trend, ordered_timeframes
                )

                if consecutive_count >= 2:
                    # Tendência alinhada em timeframes consecutivos
                    cross_scale_patterns.append(
                        {
                            "type": "multi_timeframe_trend",
                            "confidence": avg_confidence
                            * (1 + 0.2 * consecutive_count),
                            "description": f"Tendência consistente detectada em {consecutive_count} timeframes consecutivos",
                            "timeframes": timeframes_with_trend,
                            "consecutive_strength": consecutive_count
                            / len(ordered_timeframes),
                        }
                    )

        # 2. Detecção de Divergência de Padrões
        for pattern_type in patterns_by_type:
            if len(patterns_by_type[pattern_type]) >= 2:
                # Buscar divergências entre timeframes
                lower_tf_patterns = [
                    p
                    for p in patterns_by_type[pattern_type]
                    if ordered_timeframes.index(p["timeframe"])
                    < len(ordered_timeframes) // 2
                ]
                higher_tf_patterns = [
                    p
                    for p in patterns_by_type[pattern_type]
                    if ordered_timeframes.index(p["timeframe"])
                    >= len(ordered_timeframes) // 2
                ]

                if lower_tf_patterns and higher_tf_patterns:
                    # Comparar confiança média entre os grupos
                    lower_confidence = sum(
                        p["confidence"] for p in lower_tf_patterns
                    ) / len(lower_tf_patterns)
                    higher_confidence = sum(
                        p["confidence"] for p in higher_tf_patterns
                    ) / len(higher_tf_patterns)

                    if abs(lower_confidence - higher_confidence) > 0.3:
                        # Divergência significativa
                        divergence_direction = (
                            "baixo→alto"
                            if lower_confidence < higher_confidence
                            else "alto→baixo"
                        )
                        cross_scale_patterns.append(
                            {
                                "type": "timeframe_divergence",
                                "pattern_type": pattern_type,
                                "confidence": abs(lower_confidence - higher_confidence)
                                * 0.8,
                                "description": f"Divergência de {pattern_type} entre timeframes ({divergence_direction})",
                                "lower_timeframes": [
                                    p["timeframe"] for p in lower_tf_patterns
                                ],
                                "higher_timeframes": [
                                    p["timeframe"] for p in higher_tf_patterns
                                ],
                                "divergence_magnitude": abs(
                                    lower_confidence - higher_confidence
                                ),
                            }
                        )

        # 3. Padrão de Transferência de Energia entre Escalas
        volatility_patterns = patterns_by_type.get("explosive_volatility", [])
        exhaustion_patterns = patterns_by_type.get("exhaustion", [])

        if volatility_patterns and exhaustion_patterns:
            volatility_tfs = [p["timeframe"] for p in volatility_patterns]
            exhaustion_tfs = [p["timeframe"] for p in exhaustion_patterns]

            # Verificar se temos volatilidade em TFs menores e esgotamento em
            # maiores
            volatility_indices = [
                ordered_timeframes.index(tf)
                for tf in volatility_tfs
                if tf in ordered_timeframes
            ]
            exhaustion_indices = [
                ordered_timeframes.index(tf)
                for tf in exhaustion_tfs
                if tf in ordered_timeframes
            ]

            if volatility_indices and exhaustion_indices:
                avg_volatility_idx = sum(volatility_indices) / len(volatility_indices)
                avg_exhaustion_idx = sum(exhaustion_indices) / len(exhaustion_indices)

                if avg_volatility_idx < avg_exhaustion_idx:
                    # Transferência de energia: volatilidade em TFs menores →
                    # esgotamento em TFs maiores
                    cross_scale_patterns.append(
                        {
                            "type": "energy_transfer_up",
                            "confidence": 0.7,
                            "description": "Transferência de energia para cima: volatilidade em timeframes menores levando a esgotamento em timeframes maiores",
                            "volatility_timeframes": volatility_tfs,
                            "exhaustion_timeframes": exhaustion_tfs,
                        }
                    )
                elif avg_volatility_idx > avg_exhaustion_idx:
                    # Transferência de energia: esgotamento em TFs menores →
                    # volatilidade em TFs maiores
                    cross_scale_patterns.append(
                        {
                            "type": "energy_transfer_down",
                            "confidence": 0.65,
                            "description": "Transferência de energia para baixo: esgotamento em timeframes maiores levando a volatilidade em timeframes menores",
                            "volatility_timeframes": volatility_tfs,
                            "exhaustion_timeframes": exhaustion_tfs,
                        }
                    )

        # 4. Detectar conflito entre consolidação e volatilidade
        consolidation_patterns = patterns_by_type.get("consolidation", [])

        if consolidation_patterns and volatility_patterns:
            # Verificar se temos consolidação em um timeframe e volatilidade em
            # outro
            consolidation_tfs = set(p["timeframe"] for p in consolidation_patterns)
            volatility_tfs = set(p["timeframe"] for p in volatility_patterns)

            # Verificar intersecção
            if not consolidation_tfs.intersection(volatility_tfs):
                cross_scale_patterns.append(
                    {
                        "type": "consolidation_volatility_tension",
                        "confidence": 0.75,
                        "description": "Tensão entre consolidação e volatilidade em diferentes timeframes",
                        "consolidation_timeframes": list(consolidation_tfs),
                        "volatility_timeframes": list(volatility_tfs),
                    }
                )

        # Atualizar correlações entre escalas
        self._update_cross_scale_correlations(patterns_by_timeframe)

        return cross_scale_patterns

    def _evaluate_pattern_trading_relevance(
        self,
        cross_scale_patterns: List[Dict[str, Any]],
        patterns_by_timeframe: Dict[str, List[Dict[str, Any]]],
    ) -> Dict[str, Any]:
        """
        Avalia a relevância dos padrões detectados para trading.

        Args:
            cross_scale_patterns: Lista de padrões multiescala
            patterns_by_timeframe: Dicionário com padrões por timeframe

        Returns:
            Dicionário com sinais de trading
        """
        # Estrutura básica para sinal de trading
        trading_signal = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "signals": [],
            "primary_timeframe": "",
            "confidence": 0.0,
            "risk_assessment": {
                "level": "MÉDIO",
                "probability": 0.5,
                "volatility_expectation": "normal",
            },
        }

        # Verificar se temos padrões suficientes para gerar sinais
        if not cross_scale_patterns and not any(patterns_by_timeframe.values()):
            trading_signal["signals"].append(
                {
                    "type": "info",
                    "message": "Nenhum padrão significativo detectado",
                    "confidence": 0.0,
                }
            )
            return trading_signal

        # Nível de confiança geral baseado na quantidade de padrões
        pattern_count = len(cross_scale_patterns) + sum(
            len(patterns) for patterns in patterns_by_timeframe.values()
        )
        base_confidence = min(0.3 + (pattern_count * 0.05), 0.9)

        # 1. Processar padrões multiescala para sinais
        for pattern in cross_scale_patterns:
            pattern_type = pattern["type"]
            confidence = pattern["confidence"]

            if pattern_type == "multi_timeframe_trend":
                # Tendência consistente em múltiplos timeframes
                trading_signal["signals"].append(
                    {
                        "type": "trend_alignment",
                        "direction": "bullish",  # Determinar direção requer dados adicionais
                        "message": f"Tendência alinhada em múltiplos timeframes: {', '.join(pattern['timeframes'])}",
                        "confidence": confidence,
                        "timeframes": pattern["timeframes"],
                        "recommended_action": "follow_trend",
                    }
                )

                # Definir timeframe primário (o maior dos timeframes alinhados)
                if pattern["timeframes"]:
                    ordered_tfs = self._order_timeframes(pattern["timeframes"])
                    if ordered_tfs:
                        trading_signal["primary_timeframe"] = ordered_tfs[-1]

            elif pattern_type == "timeframe_divergence":
                # Divergência entre timeframes
                trading_signal["signals"].append(
                    {
                        "type": "divergence_alert",
                        "message": pattern["description"],
                        "confidence": confidence,
                        "recommended_action": "caution",
                        "affected_pattern": pattern["pattern_type"],
                    }
                )

                # Aumentar nível de risco percebido
                trading_signal["risk_assessment"]["level"] = "ALTO"
                trading_signal["risk_assessment"]["probability"] = 0.7

            elif pattern_type in ("energy_transfer_up", "energy_transfer_down"):
                # Transferência de energia entre escalas
                direction = (
                    "de baixo para cima"
                    if pattern_type == "energy_transfer_up"
                    else "de cima para baixo"
                )
                trading_signal["signals"].append(
                    {
                        "type": "energy_transfer",
                        "message": f"Transferência de energia {direction} detectada",
                        "confidence": confidence,
                        "recommended_action": (
                            "prepare_for_expansion"
                            if pattern_type == "energy_transfer_up"
                            else "prepare_for_breakdown"
                        ),
                        "volatility_expectation": "increasing",
                    }
                )

                # Atualizar expectativa de volatilidade
                trading_signal["risk_assessment"]["volatility_expectation"] = "high"

            elif pattern_type == "consolidation_volatility_tension":
                # Tensão entre consolidação e volatilidade
                trading_signal["signals"].append(
                    {
                        "type": "breakout_potential",
                        "message": "Potencial de rompimento detectado (tensão entre consolidação e volatilidade)",
                        "confidence": confidence,
                        "recommended_action": "prepare_for_breakout",
                    }
                )

                # Atualizar expectativa de volatilidade
                trading_signal["risk_assessment"][
                    "volatility_expectation"
                ] = "increasing"

        # 2. Processar padrões específicos por timeframe
        for tf, patterns in patterns_by_timeframe.items():
            for pattern in patterns:
                pattern_type = pattern["type"]
                confidence = pattern["confidence"]

                if pattern_type == "emerging_trend" and confidence > 0.6:
                    trading_signal["signals"].append(
                        {
                            "type": "single_tf_trend",
                            "timeframe": tf,
                            "message": f"Tendência emergente detectada no timeframe {tf}",
                            "confidence": confidence,
                            "recommended_action": "monitor_trend_development",
                        }
                    )

                elif pattern_type == "potential_reversal" and confidence > 0.65:
                    trading_signal["signals"].append(
                        {
                            "type": "reversal_alert",
                            "timeframe": tf,
                            "message": f"Potencial reversão no timeframe {tf}",
                            "confidence": confidence,
                            "recommended_action": "prepare_for_reversal",
                        }
                    )

                elif pattern_type == "explosive_volatility" and confidence > 0.7:
                    trading_signal["signals"].append(
                        {
                            "type": "volatility_alert",
                            "timeframe": tf,
                            "message": f"Alerta de volatilidade explosiva no timeframe {tf}",
                            "confidence": confidence,
                            "recommended_action": "widen_stops",
                        }
                    )

                    # Atualizar expectativa de volatilidade
                    trading_signal["risk_assessment"][
                        "volatility_expectation"
                    ] = "very_high"

                elif pattern_type == "qast_identified_pattern" and confidence > 0.6:
                    trading_signal["signals"].append(
                        {
                            "type": "qast_pattern",
                            "timeframe": tf,
                            "message": f"Padrão identificado pelo ciclo QAST no timeframe {tf}",
                            "confidence": confidence,
                            "recommended_action": "consult_qast_details",
                        }
                    )

        # Ordenar sinais por confiança
        trading_signal["signals"] = sorted(
            trading_signal["signals"], key=lambda s: s["confidence"], reverse=True
        )

        # Definir confiança geral baseada nos sinais de maior confiança e
        # quantidade
        if trading_signal["signals"]:
            top_confidences = [s["confidence"] for s in trading_signal["signals"][:3]]
            signal_confidence = (
                sum(top_confidences) / len(top_confidences) if top_confidences else 0.0
            )
            trading_signal["confidence"] = (base_confidence + signal_confidence) / 2
        else:
            trading_signal["confidence"] = 0.0

        # Se não há um timeframe primário definido e temos sinais, usar o maior
        # timeframe com padrões
        if not trading_signal["primary_timeframe"] and trading_signal["signals"]:
            # Timeframes com padrões, ordenados do maior para o menor
            tfs_with_patterns = [
                tf for tf in patterns_by_timeframe if patterns_by_timeframe[tf]
            ]
            if tfs_with_patterns:
                ordered_tfs = self._order_timeframes(tfs_with_patterns)
                trading_signal["primary_timeframe"] = (
                    ordered_tfs[-1] if ordered_tfs else tfs_with_patterns[0]
                )

        return trading_signal

    def _calculate_overall_confidence(
        self,
        patterns_by_timeframe: Dict[str, List[Dict[str, Any]]],
        cross_scale_patterns: List[Dict[str, Any]],
    ) -> float:
        """
        Calcula a confiança geral nas detecções de padrões.

        Args:
            patterns_by_timeframe: Dicionário com padrões por timeframe
            cross_scale_patterns: Lista de padrões multiescala

        Returns:
            Valor de confiança entre 0 e 1
        """
        if not cross_scale_patterns and not any(patterns_by_timeframe.values()):
            return 0.0

        # Calcular média ponderada das confianças
        total_confidence = 0.0
        total_weight = 0.0

        # Adicionar confianças de padrões por timeframe
        for tf, patterns in patterns_by_timeframe.items():
            if patterns:
                avg_tf_confidence = sum(p["confidence"] for p in patterns) / len(
                    patterns
                )
                # Peso menor para padrões em timeframes individuais
                total_confidence += avg_tf_confidence * 0.7
                total_weight += 0.7

        # Adicionar confianças de padrões multiescala (peso maior)
        if cross_scale_patterns:
            avg_cross_scale_confidence = sum(
                p["confidence"] for p in cross_scale_patterns
            ) / len(cross_scale_patterns)
            # Peso maior para padrões multiescala
            total_confidence += avg_cross_scale_confidence * 1.5
            total_weight += 1.5

        # Calcular média ponderada
        if total_weight > 0:
            return min(total_confidence / total_weight, 1.0)
        else:
            return 0.0

    def _order_timeframes(self, timeframes: List[str]) -> List[str]:
        """
        Ordena timeframes do menor para o maior.

        Args:
            timeframes: Lista de timeframes a ordenar

        Returns:
            Lista ordenada de timeframes
        """
        tf_minutes: Dict[str, int] = {}

        for tf in timeframes:
            try:
                minutes = timeframe_to_minutes(tf)
            except ValueError:
                minutes = 9_999_999
            tf_minutes[tf] = minutes

        return sorted(timeframes, key=lambda x: tf_minutes.get(x, 9_999_999))

    def _count_consecutive_timeframes(
        self, timeframes: List[str], ordered_timeframes: List[str]
    ) -> int:
        """
        Conta quantos timeframes consecutivos estão presentes.

        Args:
            timeframes: Lista de timeframes a verificar
            ordered_timeframes: Lista completa de timeframes ordenados

        Returns:
            Número de timeframes consecutivos
        """
        # Verificar se pelo menos um dos timeframes está na lista ordenada
        valid_tfs = [tf for tf in timeframes if tf in ordered_timeframes]
        if not valid_tfs:
            return 0

        # Obter índices dos timeframes na lista ordenada
        indices = [ordered_timeframes.index(tf) for tf in valid_tfs]
        indices.sort()

        # Contar sequências consecutivas
        max_consecutive = 1
        current_consecutive = 1

        for i in range(1, len(indices)):
            if indices[i] == indices[i - 1] + 1:
                current_consecutive += 1
            else:
                max_consecutive = max(max_consecutive, current_consecutive)
                current_consecutive = 1

        return max(max_consecutive, current_consecutive)

    def _update_cross_scale_correlations(
        self, patterns_by_timeframe: Dict[str, List[Dict[str, Any]]]
    ):
        """
        Atualiza as correlações entre diferentes escalas temporais.

        Args:
            patterns_by_timeframe: Dicionário com padrões por timeframe
        """
        timestamp = datetime.now(timezone.utc).isoformat()
        timeframes = list(patterns_by_timeframe.keys())

        # Criar matriz de correlação
        correlation_matrix = {}

        for tf1 in timeframes:
            correlation_matrix[tf1] = {}
            patterns1 = patterns_by_timeframe.get(tf1, [])

            for tf2 in timeframes:
                if tf1 == tf2:
                    correlation_matrix[tf1][tf2] = 1.0
                    continue

                patterns2 = patterns_by_timeframe.get(tf2, [])

                # Se não há padrões em algum dos timeframes, correlação zero
                if not patterns1 or not patterns2:
                    correlation_matrix[tf1][tf2] = 0.0
                    continue

                # Calcular correlação entre padrões
                correlation = self._calculate_pattern_correlation(patterns1, patterns2)
                correlation_matrix[tf1][tf2] = correlation

        # Registrar correlação
        self.cross_scale_correlations[timestamp] = correlation_matrix

        # Limitar histórico
        correlation_keys = list(self.cross_scale_correlations.keys())
        if len(correlation_keys) > 100:
            oldest_key = min(correlation_keys)
            del self.cross_scale_correlations[oldest_key]

    def _calculate_pattern_correlation(
        self, patterns1: List[Dict[str, Any]], patterns2: List[Dict[str, Any]]
    ) -> float:
        """
        Calcula correlação entre dois conjuntos de padrões.

        Args:
            patterns1: Primeiro conjunto de padrões
            patterns2: Segundo conjunto de padrões

        Returns:
            Valor de correlação entre -1 e 1
        """
        # Extrair tipos de padrões
        types1 = set(p["type"] for p in patterns1)
        types2 = set(p["type"] for p in patterns2)

        # Interseção de tipos
        common_types = types1.intersection(types2)

        # Se não há tipos em comum, correlação zero
        if not common_types:
            return 0.0

        # Calcular correlação baseada em tipos e confianças dos padrões comuns
        correlation_sum = 0.0

        for pattern_type in common_types:
            patterns1_of_type = [p for p in patterns1 if p["type"] == pattern_type]
            patterns2_of_type = [p for p in patterns2 if p["type"] == pattern_type]

            if patterns1_of_type and patterns2_of_type:
                confidence1 = sum(p["confidence"] for p in patterns1_of_type) / len(
                    patterns1_of_type
                )
                confidence2 = sum(p["confidence"] for p in patterns2_of_type) / len(
                    patterns2_of_type
                )

                # Correlação baseada na similaridade de confianças
                type_correlation = 1.0 - min(
                    abs(confidence1 - confidence2)
                    / max(confidence1, confidence2, 0.001),
                    1.0,
                )
                correlation_sum += type_correlation

        # Média das correlações
        if common_types:
            return correlation_sum / len(common_types)
        else:
            return 0.0

    def get_pattern_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Retorna o histórico recente de padrões detectados.

        Args:
            limit: Número máximo de registros a retornar

        Returns:
            Lista com os últimos padrões detectados
        """
        return (
            self.detected_patterns_history[-limit:]
            if self.detected_patterns_history
            else []
        )

    def get_cross_scale_correlation_history(self, limit: int = 10) -> Dict[str, Any]:
        """
        Retorna o histórico de correlações entre escalas temporais.

        Args:
            limit: Número máximo de registros a retornar

        Returns:
            Dicionário com o histórico de correlações
        """
        # Obter as chaves mais recentes
        sorted_keys = sorted(self.cross_scale_correlations.keys())[-limit:]

        # Construir histórico limitado
        limited_history = {
            key: self.cross_scale_correlations[key] for key in sorted_keys
        }

        return limited_history
