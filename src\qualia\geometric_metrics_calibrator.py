"""
Sistema de Calibração Unificado de Métricas QUALIA
Calibra TODOS os thresholds baseado em simulação histórica e performance real
Inclui: consciousness, coherence, confidence, volume_surge_min, momentum_min,
        spectral_phi_alignment_min, golden_symmetry_min
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta, timezone
import asyncio
from dataclasses import dataclass
import json
from pathlib import Path

from .config_manager import get_config_manager
from .binance_system import QualiaBinanceCorrectedSystem
from qsi.resonator import GeometricQSIResonator
from .metrics import calculate_geometric_metrics
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class CalibrationPoint:
    """Ponto de calibração com métricas e resultado"""
    timestamp: datetime
    symbol: str
    spectral_phi_alignment: float
    golden_symmetry: float
    geometric_coherence: float
    consciousness: float
    coherence: float
    confidence: float
    volume_surge: float
    momentum: float
    future_return_1h: float
    future_return_4h: float
    was_profitable: bool

@dataclass
class CalibrationResults:
    """Resultados da calibração"""
    symbol: str
    total_points: int
    profitable_points: int
    success_rate: float
    recommended_thresholds: Dict[str, float]
    percentile_analysis: Dict[str, Dict[str, float]]

class QualiaMetricsCalibrator:
    """Calibrador unificado de TODAS as métricas QUALIA baseado em simulação histórica"""
    
    def __init__(self, trading_system: QualiaBinanceCorrectedSystem):
        self.trading_system = trading_system
        self.config_manager = get_config_manager()
        self.resonator = GeometricQSIResonator(
            dimensions=5,
            phi1=1.618,
            phi2=2.618
        )
        self.calibration_data: List[CalibrationPoint] = []
        self.results_cache: Dict[str, CalibrationResults] = {}
        
    async def calibrate_all_assets(self,
                                 days_back: int = 30,
                                 profit_threshold: float = 0.02,
                                 time_horizon_hours: int = 4) -> Dict[str, CalibrationResults]:
        """
        Calibra TODAS as métricas QUALIA para todos os ativos monitorados

        Args:
            days_back: Dias de histórico para análise
            profit_threshold: Threshold de lucro para considerar sucesso (2% = 0.02)
            time_horizon_hours: Horizonte temporal para verificar lucro
        """
        logger.info(" Iniciando calibração UNIFICADA de métricas QUALIA...")
        logger.info(f"   Período: {days_back} dias | Lucro mínimo: {profit_threshold:.1%} | Horizonte: {time_horizon_hours}h")
        logger.info("   Métricas: consciousness, coherence, confidence, volume_surge, momentum, spectral_phi, golden_symmetry")
        
        results = {}
        assets = self.trading_system.all_assets
        
        for i, symbol in enumerate(assets, 1):
            logger.info(f" Calibrando {symbol} ({i}/{len(assets)})...")
            
            try:
                result = await self._calibrate_single_asset(
                    symbol, days_back, profit_threshold, time_horizon_hours
                )
                results[symbol] = result
                
                logger.info(f" {symbol}: {result.success_rate:.1%} sucesso "
                          f"({result.profitable_points}/{result.total_points} pontos)")
                
            except Exception as e:
                logger.error(f" Erro calibrando {symbol}: {e}")
                continue
                
            # Pequena pausa para não sobrecarregar API
            await asyncio.sleep(0.5)
        
        # Calcular thresholds agregados
        aggregated_thresholds = self._calculate_aggregated_thresholds(results)
        
        # Salvar resultados
        await self._save_calibration_results(results, aggregated_thresholds)
        
        logger.info(" Calibração concluída!")
        return results
    
    async def _calibrate_single_asset(self,
                                    symbol: str,
                                    days_back: int,
                                    profit_threshold: float,
                                    time_horizon_hours: int) -> CalibrationResults:
        """Calibra métricas para um ativo específico usando dados históricos REAIS"""

        # 1. Obter dados históricos REAIS da API Binance
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(days=days_back)

        logger.info(f" Obtendo dados históricos REAIS para {symbol}: {start_time.date()} a {end_time.date()}")

        # Usar método de dados históricos reais do sistema
        df = await self._fetch_historical_data(symbol, start_time, end_time)

        if df is None or len(df) < (days_back * 24):  # Mínimo de candles esperados (1h timeframe)
            raise ValueError(f"Dados históricos insuficientes para {symbol}: {len(df) if df is not None else 0} candles")

        logger.info(f" {symbol}: {len(df)} candles históricos obtidos ({df.index[0]} a {df.index[-1]})")
        calibration_points = []
        
        # 2. Simular cálculo de métricas em cada ponto
        for i in range(len(df) - time_horizon_hours - 1):
            try:
                # Simular market_data para este ponto temporal
                historical_market_data = {
                    'symbol': symbol,
                    'ohlcv_df': df.iloc[:i+20],  # Usar dados até este ponto
                    'price': df['close'].iloc[i],
                    'spread': 0.001,  # Estimativa
                    'volume_24h': df['volume'].iloc[i] * 24
                }
                
                # Calcular métricas como o sistema faria
                quantum_metrics = self.trading_system.calculate_enhanced_quantum_metrics(historical_market_data)
                if not quantum_metrics:
                    continue
                
                # Calcular retorno futuro
                current_price = df['close'].iloc[i]
                future_price_1h = df['close'].iloc[i + 1] if i + 1 < len(df) else current_price
                future_price_4h = df['close'].iloc[i + time_horizon_hours] if i + time_horizon_hours < len(df) else current_price
                
                future_return_1h = (future_price_1h - current_price) / current_price
                future_return_4h = (future_price_4h - current_price) / current_price
                
                was_profitable = abs(future_return_4h) >= profit_threshold and future_return_4h > 0
                
                # Criar ponto de calibração
                point = CalibrationPoint(
                    timestamp=df.index[i],
                    symbol=symbol,
                    spectral_phi_alignment=quantum_metrics.get('spectral_phi_alignment', 0),
                    golden_symmetry=quantum_metrics.get('golden_symmetry', 0),
                    geometric_coherence=quantum_metrics.get('geometric_coherence', 0),
                    consciousness=quantum_metrics['consciousness'],
                    coherence=quantum_metrics['coherence'],
                    confidence=quantum_metrics['confidence'],
                    volume_surge=quantum_metrics['volume_surge'],
                    momentum=quantum_metrics['momentum'],
                    future_return_1h=future_return_1h,
                    future_return_4h=future_return_4h,
                    was_profitable=was_profitable
                )
                
                calibration_points.append(point)
                
            except Exception as e:
                logger.debug(f"Erro processando ponto {i} para {symbol}: {e}")
                continue
        
        # 3. Analisar pontos lucrativos
        profitable_points = [p for p in calibration_points if p.was_profitable]
        
        if len(profitable_points) < 10:
            logger.warning(f"Poucos pontos lucrativos para {symbol}: {len(profitable_points)}")
        
        # 4. Calcular percentis das métricas dos pontos lucrativos
        percentile_analysis = self._calculate_percentiles(profitable_points)
        
        # 5. Recomendar thresholds (percentil 15-25% para ser seletivo)
        recommended_thresholds = {
            # Métricas principais (percentil 20-25% para seletividade)
            'consciousness': percentile_analysis['consciousness']['p20'],
            'coherence': percentile_analysis['coherence']['p20'],
            'confidence': percentile_analysis['confidence']['p20'],
            'volume_surge_min': percentile_analysis['volume_surge']['p25'],
            'momentum_min': percentile_analysis['momentum']['p15'],  # Mais permissivo para momentum
            # Métricas geométricas (percentil 20% para seletividade)
            'spectral_phi_alignment_min': percentile_analysis['spectral_phi_alignment']['p20'],
            'golden_symmetry_min': percentile_analysis['golden_symmetry']['p20']
        }
        
        return CalibrationResults(
            symbol=symbol,
            total_points=len(calibration_points),
            profitable_points=len(profitable_points),
            success_rate=len(profitable_points) / len(calibration_points) if calibration_points else 0,
            recommended_thresholds=recommended_thresholds,
            percentile_analysis=percentile_analysis
        )

    async def _fetch_historical_data(self, symbol: str, start_time: datetime, end_time: datetime) -> Optional[pd.DataFrame]:
        """
        Obtém dados históricos REAIS da API Binance

        Args:
            symbol: Símbolo do ativo
            start_time: Data/hora de início
            end_time: Data/hora de fim

        Returns:
            DataFrame com dados OHLCV históricos ou None se falhar
        """
        try:
            # Verificar se o trading system tem acesso à integração de mercado
            if not hasattr(self.trading_system, 'exchange') or not self.trading_system.exchange:
                logger.error(" Sistema de trading não tem acesso à exchange")
                return None

            # Usar timeframe de 1 hora para ter dados suficientes mas não excessivos
            timeframe = '1h'

            # Calcular limite de candles necessários
            hours_diff = int((end_time - start_time).total_seconds() / 3600)
            limit = min(hours_diff + 24, 1000)  # Máximo 1000 candles por limitação da API

            logger.debug(f" Buscando {limit} candles de {timeframe} para {symbol}")

            # Método 1: Tentar usar fetch_ohlcv com since
            since_timestamp = int(start_time.timestamp() * 1000)  # Converter para milliseconds

            try:
                ohlcv_data = self.trading_system.exchange.fetch_ohlcv(
                    symbol,
                    timeframe,
                    since=since_timestamp,
                    limit=limit
                )

                if ohlcv_data and len(ohlcv_data) > 0:
                    # Converter para DataFrame
                    df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
                    df.set_index('timestamp', inplace=True)

                    # Converter start_time e end_time para timezone-aware se necessário
                    start_time_tz = start_time.replace(tzinfo=timezone.utc) if start_time.tzinfo is None else start_time
                    end_time_tz = end_time.replace(tzinfo=timezone.utc) if end_time.tzinfo is None else end_time

                    # Filtrar pelo período desejado
                    df = df[(df.index >= start_time_tz) & (df.index <= end_time_tz)]

                    logger.debug(f"Método 1 sucesso: {len(df)} candles obtidos")
                    return df

            except Exception as e:
                logger.warning(f" Método 1 falhou para {symbol}: {e}")

            # Método 2: Tentar usar fetch_ohlcv sem since (dados mais recentes)
            try:
                ohlcv_data = self.trading_system.exchange.fetch_ohlcv(
                    symbol,
                    timeframe,
                    limit=limit
                )

                if ohlcv_data and len(ohlcv_data) > 0:
                    # Converter para DataFrame
                    df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
                    df.set_index('timestamp', inplace=True)

                    # Converter start_time para timezone-aware se necessário
                    start_time_tz = start_time.replace(tzinfo=timezone.utc) if start_time.tzinfo is None else start_time

                    # Verificar se temos dados suficientes no período
                    recent_data = df[df.index >= start_time_tz]
                    if len(recent_data) >= (hours_diff * 0.5):  # Pelo menos 50% dos dados esperados
                        logger.debug(f"Método 2 sucesso: {len(recent_data)} candles obtidos")
                        return recent_data

            except Exception as e:
                logger.warning(f" Método 2 falhou para {symbol}: {e}")

            # Método 3: Tentar usar método de dados históricos do sistema (se disponível)
            try:
                if hasattr(self.trading_system, 'market_integration') and self.trading_system.market_integration:
                    from qsi.qualia.market.base_integration import MarketSpec

                    spec = MarketSpec(symbol=symbol, timeframe=timeframe)
                    df = await self.trading_system.market_integration.fetch_historical_data(
                        spec=spec,
                        start_date=start_time,
                        end_date=end_time,
                        use_cache=False
                    )

                    if df is not None and not df.empty:
                        logger.debug(f" Método 3 sucesso: {len(df)} candles obtidos")
                        return df

            except Exception as e:
                logger.warning(f" Método 3 falhou para {symbol}: {e}")

            logger.error(f" Todos os métodos falharam para obter dados históricos de {symbol}")
            return None

        except Exception as e:
            logger.error(f" Erro geral obtendo dados históricos para {symbol}: {e}")
            return None
    
    def _calculate_percentiles(self, points: List[CalibrationPoint]) -> Dict[str, Dict[str, float]]:
        """Calcula percentis das métricas"""
        if not points:
            return {}
        
        metrics = {
            'spectral_phi_alignment': [p.spectral_phi_alignment for p in points],
            'golden_symmetry': [p.golden_symmetry for p in points],
            'geometric_coherence': [p.geometric_coherence for p in points],
            'consciousness': [p.consciousness for p in points],
            'coherence': [p.coherence for p in points],
            'confidence': [p.confidence for p in points],
            'volume_surge': [p.volume_surge for p in points],
            'momentum': [abs(p.momentum) for p in points]
        }
        
        percentile_analysis = {}
        for metric_name, values in metrics.items():
            if values:
                percentile_analysis[metric_name] = {
                    'p10': float(np.percentile(values, 10)),
                    'p15': float(np.percentile(values, 15)),
                    'p20': float(np.percentile(values, 20)),
                    'p25': float(np.percentile(values, 25)),
                    'p30': float(np.percentile(values, 30)),
                    'median': float(np.median(values)),
                    'mean': float(np.mean(values)),
                    'std': float(np.std(values))
                }
        
        return percentile_analysis
    
    def _calculate_aggregated_thresholds(self, results: Dict[str, CalibrationResults]) -> Dict[str, float]:
        """Calcula thresholds agregados de todos os ativos para TODAS as métricas"""
        if not results:
            return {}

        # Lista completa de métricas QUALIA
        all_metrics = [
            'consciousness', 'coherence', 'confidence',
            'volume_surge_min', 'momentum_min',
            'spectral_phi_alignment_min', 'golden_symmetry_min'
        ]

        # Coletar thresholds recomendados de todos os ativos
        aggregated_thresholds = {}
        for metric in all_metrics:
            values = []
            for result in results.values():
                if metric in result.recommended_thresholds:
                    values.append(result.recommended_thresholds[metric])

            if values:
                # Usar mediana para robustez contra outliers
                aggregated_thresholds[metric] = float(np.median(values))
                logger.debug(f" {metric}: {len(values)} valores, mediana = {aggregated_thresholds[metric]:.3f}")

        return aggregated_thresholds
    
    async def _save_calibration_results(self, results: Dict[str, CalibrationResults], 
                                      aggregated_thresholds: Dict[str, float]):
        """Salva resultados da calibração"""
        timestamp = datetime.now(timezone.utc).isoformat()
        
        calibration_report = {
            'timestamp': timestamp,
            'aggregated_thresholds': aggregated_thresholds,
            'individual_results': {
                symbol: {
                    'total_points': result.total_points,
                    'profitable_points': result.profitable_points,
                    'success_rate': result.success_rate,
                    'recommended_thresholds': result.recommended_thresholds,
                    'percentile_analysis': result.percentile_analysis
                }
                for symbol, result in results.items()
            }
        }
        
        # Salvar em arquivo
        calibration_dir = Path('data/calibration')
        calibration_dir.mkdir(parents=True, exist_ok=True)
        
        filename = f"geometric_metrics_calibration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = calibration_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(calibration_report, f, indent=2, default=str)
        
        logger.info(f"Resultados salvos em: {filepath}")

        # Log resumo
        logger.info("THRESHOLDS RECOMENDADOS:")
        for metric, value in aggregated_thresholds.items():
            logger.info(f"   {metric}: {value:.3f}")

    async def apply_calibrated_thresholds(self, results: Dict[str, CalibrationResults],
                                        gradual_factor: float = 0.7):
        """
        Aplica TODOS os thresholds calibrados ao sistema de forma unificada

        Args:
            results: Resultados da calibração
            gradual_factor: Fator de aplicação gradual (0.7 = 70% novo, 30% antigo)
        """
        aggregated = self._calculate_aggregated_thresholds(results)

        if not aggregated:
            logger.warning("Nenhum threshold calibrado disponivel")
            return

        logger.info("Aplicando thresholds calibrados ao sistema...")

        # Atualizar TODOS os thresholds no sistema de trading
        updated_thresholds = {}

        for metric, new_value in aggregated.items():
            # Obter valor atual
            current_value = self.trading_system.quantum_thresholds.get(metric, new_value)

            # Aplicar mudança gradual para estabilidade
            final_value = (new_value * gradual_factor) + (current_value * (1 - gradual_factor))
            updated_thresholds[metric] = final_value

            logger.info(f"AJUSTE {metric}: {current_value:.3f} -> {final_value:.3f} "
                       f"(calibrado: {new_value:.3f})")

        # Atualizar sistema de trading com TODOS os thresholds
        self.trading_system.quantum_thresholds.update(updated_thresholds)

        # Atualizar adaptive_manager se disponível
        if hasattr(self.trading_system, 'adaptive_manager') and self.trading_system.adaptive_manager:
            # Atualizar thresholds no adaptive_manager
            current_thresholds = self.trading_system.adaptive_manager.current_thresholds

            # Mapear para campos do ThresholdConfig
            threshold_mapping = {
                'consciousness': 'consciousness',
                'coherence': 'coherence',
                'confidence': 'confidence',
                'volume_surge_min': 'volume_surge_min',
                'momentum_min': 'momentum_min',
                'spectral_phi_alignment_min': 'spectral_phi_alignment_min',
                'golden_symmetry_min': 'golden_symmetry_min'
            }

            for metric, field in threshold_mapping.items():
                if metric in updated_thresholds and hasattr(current_thresholds, field):
                    setattr(current_thresholds, field, updated_thresholds[metric])
                    logger.debug(f" Adaptive manager {field} atualizado: {updated_thresholds[metric]:.3f}")

            # Atualizar TODOS os trading modes no adaptive_manager
            await self._update_adaptive_manager_trading_modes(updated_thresholds)

        # Persistir thresholds no arquivo de configuração
        await self._persist_thresholds_to_config(updated_thresholds)

        logger.info("OK TODOS os thresholds aplicados ao sistema!")

    async def _persist_thresholds_to_config(self, updated_thresholds: Dict[str, float]):
        """
        Persiste os thresholds calibrados no arquivo qualia_config.yaml

        Args:
            updated_thresholds: Dicionário com os novos thresholds
        """
        try:
            import yaml
            from pathlib import Path

            # Caminho para o arquivo de configuração
            config_path = Path('config/qualia_config.yaml')

            if not config_path.exists():
                logger.error(f"Arquivo de configuracao nao encontrado: {config_path}")
                return

            # Ler configuração atual
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # Backup do arquivo original
            backup_path = config_path.with_suffix('.yaml.backup')
            with open(backup_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

            logger.info(f"Backup criado: {backup_path}")

            # Atualizar thresholds na configuração
            if 'quantum_thresholds' not in config:
                config['quantum_thresholds'] = {}

            original_values = {}
            for metric, new_value in updated_thresholds.items():
                # Salvar valor original para log
                original_values[metric] = config['quantum_thresholds'].get(metric, 'N/A')

                # Atualizar valor base
                config['quantum_thresholds'][metric] = round(float(new_value), 3)

                logger.info(f"CONFIG {metric}: {original_values[metric]} -> {new_value:.3f}")

            # Atualizar TRADING MODES com lógica calibrada
            await self._update_trading_modes_in_config(config, updated_thresholds)

            # Adicionar metadados da calibração
            from datetime import datetime
            config['quantum_thresholds']['_calibration_metadata'] = {
                'last_calibration': datetime.now().isoformat(),
                'calibration_method': 'historical_simulation',
                'calibrated_by': 'QualiaMetricsCalibrator',
                'trading_modes_updated': True
            }

            # Salvar configuração atualizada
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)

            logger.info(f"Configuracao atualizada salva em: {config_path}")
            logger.info("PERSISTENCIA: Thresholds calibrados salvos no arquivo de configuracao!")

        except Exception as e:
            logger.error(f"Erro persistindo thresholds no arquivo de configuracao: {e}")
            logger.warning("AVISO: Thresholds aplicados apenas em memoria - serao perdidos ao reiniciar!")

    async def _update_trading_modes_in_config(self, config: Dict, calibrated_thresholds: Dict[str, float]):
        """
        Atualiza os trading_modes na configuração baseado nos thresholds calibrados

        Lógica:
        - Conservative: +20% mais restritivo (valores mais altos)
        - Moderate: baseline (valores calibrados)
        - Aggressive: -20% mais permissivo (valores mais baixos)
        """
        logger.info("🔧 Atualizando TRADING MODES com thresholds calibrados...")

        # Garantir que trading_modes existe
        if 'trading_modes' not in config['quantum_thresholds']:
            config['quantum_thresholds']['trading_modes'] = {}

        trading_modes = config['quantum_thresholds']['trading_modes']

        # Métricas que devem ser atualizadas nos trading modes
        mode_metrics = ['consciousness', 'coherence', 'confidence', 'momentum_min', 'volume_surge_min']

        # Fatores de ajuste para cada modo
        mode_factors = {
            'conservative': 1.20,    # +20% mais restritivo
            'moderate': 1.00,        # baseline
            'aggressive': 0.80       # -20% mais permissivo
        }

        # Atualizar cada modo
        for mode_name, factor in mode_factors.items():
            if mode_name not in trading_modes:
                trading_modes[mode_name] = {}

            mode_config = trading_modes[mode_name]

            logger.info(f"   Atualizando modo {mode_name.upper()} (fator: {factor:.2f})...")

            for metric in mode_metrics:
                if metric in calibrated_thresholds:
                    calibrated_value = calibrated_thresholds[metric]

                    # Aplicar fator de ajuste
                    adjusted_value = calibrated_value * factor

                    # Garantir limites razoáveis
                    adjusted_value = max(0.1, min(1.0, adjusted_value))

                    # Salvar valor original para log
                    original_value = mode_config.get(metric, 'N/A')

                    # Atualizar valor
                    mode_config[metric] = round(float(adjusted_value), 3)

                    logger.info(f"      {metric}: {original_value} -> {adjusted_value:.3f}")

        # Verificar se todos os modos têm todas as métricas necessárias
        self._ensure_complete_trading_modes(trading_modes, calibrated_thresholds)

        logger.info("✅ TRADING MODES atualizados com sucesso!")

    def _ensure_complete_trading_modes(self, trading_modes: Dict, calibrated_thresholds: Dict[str, float]):
        """Garante que todos os modos tenham todas as métricas necessárias"""
        required_metrics = ['consciousness', 'coherence', 'confidence', 'momentum_min', 'volume_surge_min']
        required_modes = ['conservative', 'moderate', 'aggressive']

        for mode_name in required_modes:
            if mode_name not in trading_modes:
                trading_modes[mode_name] = {}

            mode_config = trading_modes[mode_name]

            # Verificar se todas as métricas estão presentes
            for metric in required_metrics:
                if metric not in mode_config:
                    # Usar valor padrão baseado no calibrado ou fallback
                    if metric in calibrated_thresholds:
                        base_value = calibrated_thresholds[metric]
                    else:
                        # Valores fallback
                        fallback_values = {
                            'consciousness': 0.60,
                            'coherence': 0.50,
                            'confidence': 0.55,
                            'momentum_min': 0.003,
                            'volume_surge_min': 1.2
                        }
                        base_value = fallback_values.get(metric, 0.5)

                    # Aplicar fator do modo
                    mode_factors = {'conservative': 1.20, 'moderate': 1.00, 'aggressive': 0.80}
                    factor = mode_factors[mode_name]

                    adjusted_value = max(0.1, min(1.0, base_value * factor))
                    mode_config[metric] = round(float(adjusted_value), 3)

                    logger.warning(f"⚠️ Métrica {metric} faltando em {mode_name}, adicionada: {adjusted_value:.3f}")

    async def _update_adaptive_manager_trading_modes(self, calibrated_thresholds: Dict[str, float]):
        """Atualiza os trading modes no adaptive_manager com valores calibrados"""
        if not hasattr(self.trading_system, 'adaptive_manager') or not self.trading_system.adaptive_manager:
            return

        logger.info("🔧 Atualizando trading modes no AdaptiveThresholdManager...")

        adaptive_manager = self.trading_system.adaptive_manager

        # Importar ThresholdConfig
        from .adaptive_threshold_system import ThresholdConfig

        # Fatores para cada modo
        mode_factors = {
            'CONSERVATIVE': 1.20,    # +20% mais restritivo
            'MODERATE': 1.00,        # baseline
            'AGGRESSIVE': 0.80       # -20% mais permissivo
        }

        # Métricas base para cálculo
        base_metrics = {
            'consciousness': calibrated_thresholds.get('consciousness', 0.60),
            'coherence': calibrated_thresholds.get('coherence', 0.50),
            'confidence': calibrated_thresholds.get('confidence', 0.55),
            'volume_surge_min': calibrated_thresholds.get('volume_surge_min', 1.2),
            'momentum_min': calibrated_thresholds.get('momentum_min', 0.003),
            'spectral_phi_alignment_min': calibrated_thresholds.get('spectral_phi_alignment_min', 0.5),
            'golden_symmetry_min': calibrated_thresholds.get('golden_symmetry_min', 0.5)
        }

        # Atualizar cada modo no adaptive_manager
        from .adaptive_threshold_system import TradingMode

        for mode_enum, factor in [(TradingMode.CONSERVATIVE, 1.20),
                                 (TradingMode.MODERATE, 1.00),
                                 (TradingMode.AGGRESSIVE, 0.80)]:

            # Calcular valores ajustados
            adjusted_values = {}
            for metric, base_value in base_metrics.items():
                adjusted_value = base_value * factor
                adjusted_value = max(0.1, min(1.0, adjusted_value))  # Limitar entre 0.1 e 1.0
                adjusted_values[metric] = adjusted_value

            # Criar novo ThresholdConfig
            new_config = ThresholdConfig(
                consciousness=adjusted_values['consciousness'],
                coherence=adjusted_values['coherence'],
                confidence=adjusted_values['confidence'],
                volume_surge_min=adjusted_values['volume_surge_min'],
                momentum_min=adjusted_values['momentum_min'],
                spectral_phi_alignment_min=adjusted_values['spectral_phi_alignment_min'],
                golden_symmetry_min=adjusted_values['golden_symmetry_min']
            )

            # Atualizar no adaptive_manager
            adaptive_manager.threshold_configs[mode_enum] = new_config

            logger.info(f"   {mode_enum.value.upper()} atualizado: "
                       f"C={new_config.consciousness:.3f}, "
                       f"Coh={new_config.coherence:.3f}, "
                       f"Conf={new_config.confidence:.3f}")

        logger.info("✅ Trading modes do AdaptiveThresholdManager atualizados!")

    def get_calibration_summary(self, results: Dict[str, CalibrationResults]) -> str:
        """Gera resumo completo da calibração unificada"""
        if not results:
            return "Nenhum resultado de calibração disponível"

        total_points = sum(r.total_points for r in results.values())
        total_profitable = sum(r.profitable_points for r in results.values())
        avg_success_rate = np.mean([r.success_rate for r in results.values()])

        summary = f"""
RESUMO DA CALIBRACAO UNIFICADA QUALIA
{'='*60}
Ativos analisados: {len(results)}
Pontos totais: {total_points:,}
Pontos lucrativos: {total_profitable:,}
Taxa de sucesso media: {avg_success_rate:.1%}

THRESHOLDS RECOMENDADOS (TODAS AS METRICAS):
"""

        aggregated = self._calculate_aggregated_thresholds(results)

        # Organizar por categoria
        main_metrics = ['consciousness', 'coherence', 'confidence']
        volume_momentum = ['volume_surge_min', 'momentum_min']
        geometric = ['spectral_phi_alignment_min', 'golden_symmetry_min']

        summary += "\n    METRICAS PRINCIPAIS:\n"
        for metric in main_metrics:
            if metric in aggregated:
                summary += f"      {metric}: {aggregated[metric]:.3f}\n"

        summary += "\n    VOLUME & MOMENTUM:\n"
        for metric in volume_momentum:
            if metric in aggregated:
                summary += f"      {metric}: {aggregated[metric]:.3f}\n"

        summary += "\n    METRICAS GEOMETRICAS:\n"
        for metric in geometric:
            if metric in aggregated:
                summary += f"      {metric}: {aggregated[metric]:.3f}\n"

        return summary

    def generate_detailed_report(self, results: Dict[str, CalibrationResults]) -> Dict:
        """Gera relatório detalhado para análise"""
        if not results:
            return {}

        # Estatísticas por ativo
        asset_stats = {}
        for symbol, result in results.items():
            asset_stats[symbol] = {
                'success_rate': result.success_rate,
                'total_points': result.total_points,
                'profitable_points': result.profitable_points,
                'thresholds': result.recommended_thresholds
            }

        # Estatísticas por métrica
        metric_stats = {}
        all_metrics = ['consciousness', 'coherence', 'confidence', 'volume_surge_min',
                      'momentum_min', 'spectral_phi_alignment_min', 'golden_symmetry_min']

        for metric in all_metrics:
            values = []
            for result in results.values():
                if metric in result.recommended_thresholds:
                    values.append(result.recommended_thresholds[metric])

            if values:
                metric_stats[metric] = {
                    'count': len(values),
                    'min': float(np.min(values)),
                    'max': float(np.max(values)),
                    'median': float(np.median(values)),
                    'mean': float(np.mean(values)),
                    'std': float(np.std(values)),
                    'q25': float(np.percentile(values, 25)),
                    'q75': float(np.percentile(values, 75))
                }

        # Correlações entre métricas (se possível)
        correlations = self._calculate_metric_correlations(results)

        return {
            'asset_statistics': asset_stats,
            'metric_statistics': metric_stats,
            'correlations': correlations,
            'aggregated_thresholds': self._calculate_aggregated_thresholds(results),
            'total_assets': len(results),
            'total_points': sum(r.total_points for r in results.values()),
            'total_profitable': sum(r.profitable_points for r in results.values()),
            'average_success_rate': float(np.mean([r.success_rate for r in results.values()]))
        }

    def _calculate_metric_correlations(self, results: Dict[str, CalibrationResults]) -> Dict:
        """Calcula correlações entre métricas (análise exploratória)"""
        try:
            # Coletar dados para análise de correlação
            data = []
            for result in results.values():
                if result.recommended_thresholds:
                    data.append(result.recommended_thresholds)

            if len(data) < 3:  # Precisa de pelo menos 3 pontos para correlação
                return {}

            # Converter para DataFrame para análise
            import pandas as pd
            df = pd.DataFrame(data)

            # Calcular correlações
            correlations = {}
            for col1 in df.columns:
                correlations[col1] = {}
                for col2 in df.columns:
                    if col1 != col2:
                        corr = df[col1].corr(df[col2])
                        if not np.isnan(corr):
                            correlations[col1][col2] = float(corr)

            return correlations

        except Exception as e:
            logger.debug(f"Erro calculando correlações: {e}")
            return {}
